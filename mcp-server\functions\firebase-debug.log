[debug] [2025-07-30T13:16:59.572Z] ----------------------------------------------------------------------
[debug] [2025-07-30T13:16:59.575Z] Command:       C:\Program Files\nodejs\node.exe C:\Users\<USER>\AppData\Roaming\npm\node_modules\firebase-tools\lib\bin\firebase.js emulators:start --only functions
[debug] [2025-07-30T13:16:59.576Z] CLI Version:   14.8.0
[debug] [2025-07-30T13:16:59.576Z] Platform:      win32
[debug] [2025-07-30T13:16:59.576Z] Node Version:  v22.14.0
[debug] [2025-07-30T13:16:59.576Z] Time:          Thu Jul 31 2025 01:16:59 GMT+1200 (New Zealand Standard Time)
[debug] [2025-07-30T13:16:59.577Z] ----------------------------------------------------------------------
[debug] 
[debug] [2025-07-30T13:16:59.908Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-07-30T13:16:59.908Z] > authorizing via signed-in user (<EMAIL>)
[info] i  emulators: Starting emulators: functions {"metadata":{"emulator":{"name":"hub"},"message":"Starting emulators: functions"}}
[debug] [2025-07-30T13:16:59.921Z] [logging] Logging Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-07-30T13:16:59.921Z] assigned listening specs for emulators {"user":{"hub":[{"address":"127.0.0.1","family":"IPv4","port":4400},{"address":"::1","family":"IPv6","port":4400}],"ui":[{"address":"127.0.0.1","family":"IPv4","port":4000},{"address":"::1","family":"IPv6","port":4000}],"logging":[{"address":"127.0.0.1","family":"IPv4","port":4500}]},"metadata":{"message":"assigned listening specs for emulators"}}
[debug] [2025-07-30T13:16:59.927Z] [hub] writing locator at C:\Users\<USER>\AppData\Local\Temp\hub-kissandost-9570f.json
[debug] [2025-07-30T13:16:59.953Z] [functions] Functions Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-07-30T13:16:59.953Z] [eventarc] Eventarc Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-07-30T13:16:59.954Z] [tasks] Cloud Tasks Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-07-30T13:16:59.954Z] late-assigned ports for functions and eventarc emulators {"user":{"hub":[{"address":"127.0.0.1","family":"IPv4","port":4400},{"address":"::1","family":"IPv6","port":4400}],"ui":[{"address":"127.0.0.1","family":"IPv4","port":4000},{"address":"::1","family":"IPv6","port":4000}],"logging":[{"address":"127.0.0.1","family":"IPv4","port":4500}],"functions":[{"address":"127.0.0.1","family":"IPv4","port":5001}],"eventarc":[{"address":"127.0.0.1","family":"IPv4","port":9299}],"tasks":[{"address":"127.0.0.1","family":"IPv4","port":9499}]},"metadata":{"message":"late-assigned ports for functions and eventarc emulators"}}
[warn] !  functions: The following emulators are not running, calls to these services from the Functions emulator will affect production: apphosting, auth, firestore, database, hosting, pubsub, storage, dataconnect {"metadata":{"emulator":{"name":"functions"},"message":"The following emulators are not running, calls to these services from the Functions emulator will affect production: \u001b[1mapphosting, auth, firestore, database, hosting, pubsub, storage, dataconnect\u001b[22m"}}
[debug] [2025-07-30T13:16:59.971Z] defaultcredentials: writing to file C:\Users\<USER>\AppData\Roaming\firebase\malikeahtesham111_gmail_com_application_default_credentials.json
[debug] [2025-07-30T13:16:59.973Z] Setting GAC to C:\Users\<USER>\AppData\Roaming\firebase\malikeahtesham111_gmail_com_application_default_credentials.json {"metadata":{"emulator":{"name":"functions"},"message":"Setting GAC to C:\\Users\\<USER>\\AppData\\Roaming\\firebase\\malikeahtesham111_gmail_com_application_default_credentials.json"}}
[debug] [2025-07-30T13:16:59.974Z] Checked if tokens are valid: false, expires at: 1753875640230
[debug] [2025-07-30T13:16:59.975Z] Checked if tokens are valid: false, expires at: 1753875640230
[debug] [2025-07-30T13:16:59.975Z] > refreshing access token with scopes: []
[debug] [2025-07-30T13:16:59.978Z] >>> [apiv2][query] POST https://www.googleapis.com/oauth2/v3/token [none]
[debug] [2025-07-30T13:16:59.978Z] >>> [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-07-30T13:17:00.341Z] <<< [apiv2][status] POST https://www.googleapis.com/oauth2/v3/token 200
[debug] [2025-07-30T13:17:00.341Z] <<< [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-07-30T13:17:00.349Z] >>> [apiv2][query] GET https://firebase.googleapis.com/v1beta1/projects/kissandost-9570f/adminSdkConfig [none]
[debug] [2025-07-30T13:17:01.038Z] <<< [apiv2][status] GET https://firebase.googleapis.com/v1beta1/projects/kissandost-9570f/adminSdkConfig 200
[debug] [2025-07-30T13:17:01.038Z] <<< [apiv2][body] GET https://firebase.googleapis.com/v1beta1/projects/kissandost-9570f/adminSdkConfig {"projectId":"kissandost-9570f","databaseURL":"https://kissandost-9570f-default-rtdb.firebaseio.com","storageBucket":"kissandost-9570f.firebasestorage.app"}
[info] i  functions: Watching "E:\python_projects_factory\animal\mcp-server\functions" for Cloud Functions... {"metadata":{"emulator":{"name":"functions"},"message":"Watching \"E:\\python_projects_factory\\animal\\mcp-server\\functions\" for Cloud Functions..."}}
[debug] [2025-07-30T13:17:01.080Z] Validating nodejs source
[debug] [2025-07-30T13:17:02.646Z] > [functions] package.json contents: {
  "name": "functions",
  "description": "Cloud Functions for Firebase",
  "scripts": {
    "serve": "firebase emulators:start --only functions",
    "shell": "firebase functions:shell",
    "start": "npm run shell",
    "deploy": "firebase deploy --only functions",
    "logs": "firebase functions:log"
  },
  "engines": {
    "node": "22"
  },
  "main": "index.js",
  "dependencies": {
    "axios": "^1.10.0",
    "cors": "^2.8.5",
    "dotenv": "^17.2.1",
    "express": "^4.18.2",
    "firebase-admin": "^12.6.0",
    "firebase-functions": "^6.0.1",
    "multer": "^1.4.5-lts.1",
    "openai": "^4.20.1",
    "uuid": "^9.0.1"
  },
  "devDependencies": {
    "firebase-functions-test": "^3.1.0"
  },
  "private": true
}
[debug] [2025-07-30T13:17:02.646Z] Building nodejs source
[debug] [2025-07-30T13:17:02.646Z] Failed to find version of module node: reached end of search path E:\python_projects_factory\animal\mcp-server\functions\node_modules
[info] +  functions: Using node@22 from host. 
[info] i  functions: Loaded environment variables from .env. 
[debug] [2025-07-30T13:17:02.652Z] Could not find functions.yaml. Must use http discovery
[debug] [2025-07-30T13:17:02.660Z] Found firebase-functions binary at 'E:\python_projects_factory\animal\mcp-server\functions\node_modules\.bin\firebase-functions'
[info] Serving at port 8171

[info] [dotenv@17.2.1] injecting env (2) from .env -- tip: 📡 observe env with Radar: https://dotenvx.com/radar

[debug] [2025-07-30T13:17:03.249Z] Got response from /__/functions.yaml {"endpoints":{"animalApp":{"availableMemoryMb":null,"timeoutSeconds":null,"minInstances":null,"maxInstances":null,"ingressSettings":null,"concurrency":null,"serviceAccountEmail":null,"vpc":null,"platform":"gcfv2","region":["australia-southeast1"],"labels":{},"httpsTrigger":{},"entryPoint":"animalApp"}},"specVersion":"v1alpha1","requiredAPIs":[],"extensions":{}}
[info] +  functions: Loaded functions definitions from source: animalApp. {"metadata":{"emulator":{"name":"functions"},"message":"Loaded functions definitions from source: animalApp."}}
[info] +  functions[australia-southeast1-animalApp]: http function initialized (http://127.0.0.1:5001/kissandost-9570f/australia-southeast1/animalApp). {"metadata":{"emulator":{"name":"functions"},"message":"\u001b[1mhttp\u001b[22m function initialized (http://127.0.0.1:5001/kissandost-9570f/australia-southeast1/animalApp)."}}
[debug] [2025-07-30T13:17:07.302Z] Could not find VSCode notification endpoint: FetchError: request to http://localhost:40001/vscode/notify failed, reason: . If you are not running the Firebase Data Connect VSCode extension, this is expected and not an issue.
[info] 
┌─────────────────────────────────────────────────────────────┐
│ ✔  All emulators ready! It is now safe to connect your app. │
│ i  View Emulator UI at http://127.0.0.1:4000/               │
└─────────────────────────────────────────────────────────────┘

┌───────────┬────────────────┬─────────────────────────────────┐
│ Emulator  │ Host:Port      │ View in Emulator UI             │
├───────────┼────────────────┼─────────────────────────────────┤
│ Functions │ 127.0.0.1:5001 │ http://127.0.0.1:4000/functions │
└───────────┴────────────────┴─────────────────────────────────┘
  Emulator Hub host: 127.0.0.1 port: 4400
  Other reserved ports: 4500

Issues? Report them at https://github.com/firebase/firebase-tools/issues and attach the *-debug.log files.
 
[debug] [2025-07-30T13:17:31.243Z] [work-queue] {"queuedWork":["/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat-2025-07-30T13:17:31.242Z"],"queueLength":1,"runningWork":[],"workRunningCount":0}
[debug] [2025-07-30T13:17:31.243Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":["/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat-2025-07-30T13:17:31.242Z"],"workRunningCount":1}
[debug] [2025-07-30T13:17:31.243Z] Accepted request OPTIONS /kissandost-9570f/australia-southeast1/animalApp/open-ai-chat --> australia-southeast1-animalApp
[debug] [2025-07-30T13:17:31.247Z] [functions] Runtime ready! Sending request! {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Runtime ready! Sending request!"}}
[debug] [2025-07-30T13:17:31.247Z] [functions] Got req.url=/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat, mapping to path=/open-ai-chat {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Got req.url=/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat, mapping to path=/open-ai-chat"}}
[info] i  functions: Loaded environment variables from .env. 
[debug] [2025-07-30T13:17:31.258Z] [worker-pool] addWorker(australia-southeast1-animalApp) {"metadata":{"emulator":{"name":"functions"},"message":"[worker-pool] addWorker(australia-southeast1-animalApp)"}}
[debug] [2025-07-30T13:17:31.259Z] [worker-pool] Adding worker with key australia-southeast1-animalApp, total=1 {"metadata":{"emulator":{"name":"functions"},"message":"[worker-pool] Adding worker with key australia-southeast1-animalApp, total=1"}}
[debug] [2025-07-30T13:17:32.907Z] [runtime-status] [632] Resolved module firebase-admin {"declared":true,"installed":true,"version":"12.7.0","resolution":"E:\\python_projects_factory\\animal\\mcp-server\\functions\\node_modules\\firebase-admin\\lib\\index.js"} {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[runtime-status] [632] Resolved module firebase-admin {\"declared\":true,\"installed\":true,\"version\":\"12.7.0\",\"resolution\":\"E:\\\\python_projects_factory\\\\animal\\\\mcp-server\\\\functions\\\\node_modules\\\\firebase-admin\\\\lib\\\\index.js\"}"}}
[debug] [2025-07-30T13:17:32.908Z] [runtime-status] [632] Resolved module firebase-functions {"declared":true,"installed":true,"version":"6.4.0","resolution":"E:\\python_projects_factory\\animal\\mcp-server\\functions\\node_modules\\firebase-functions\\lib\\v2\\index.js"} {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[runtime-status] [632] Resolved module firebase-functions {\"declared\":true,\"installed\":true,\"version\":\"6.4.0\",\"resolution\":\"E:\\\\python_projects_factory\\\\animal\\\\mcp-server\\\\functions\\\\node_modules\\\\firebase-functions\\\\lib\\\\v2\\\\index.js\"}"}}
[debug] [2025-07-30T13:17:32.908Z] [runtime-status] [632] Outgoing network have been stubbed. [{"name":"http","status":"mocked"},{"name":"http","status":"mocked"},{"name":"https","status":"mocked"},{"name":"https","status":"mocked"},{"name":"net","status":"mocked"}] {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[runtime-status] [632] Outgoing network have been stubbed. [{\"name\":\"http\",\"status\":\"mocked\"},{\"name\":\"http\",\"status\":\"mocked\"},{\"name\":\"https\",\"status\":\"mocked\"},{\"name\":\"https\",\"status\":\"mocked\"},{\"name\":\"net\",\"status\":\"mocked\"}]"}}
[debug] [2025-07-30T13:17:32.909Z] [runtime-status] [632] Resolved module firebase-functions {"declared":true,"installed":true,"version":"6.4.0","resolution":"E:\\python_projects_factory\\animal\\mcp-server\\functions\\node_modules\\firebase-functions\\lib\\v2\\index.js"} {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[runtime-status] [632] Resolved module firebase-functions {\"declared\":true,\"installed\":true,\"version\":\"6.4.0\",\"resolution\":\"E:\\\\python_projects_factory\\\\animal\\\\mcp-server\\\\functions\\\\node_modules\\\\firebase-functions\\\\lib\\\\v2\\\\index.js\"}"}}
[debug] [2025-07-30T13:17:33.387Z] [runtime-status] [632] Checked functions.config() {"config":{}} {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[runtime-status] [632] Checked functions.config() {\"config\":{}}"}}
[debug] [2025-07-30T13:17:33.388Z] [runtime-status] [632] firebase-functions has been stubbed. {"functionsResolution":{"declared":true,"installed":true,"version":"6.4.0","resolution":"E:\\python_projects_factory\\animal\\mcp-server\\functions\\node_modules\\firebase-functions\\lib\\v2\\index.js"}} {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[runtime-status] [632] firebase-functions has been stubbed. {\"functionsResolution\":{\"declared\":true,\"installed\":true,\"version\":\"6.4.0\",\"resolution\":\"E:\\\\python_projects_factory\\\\animal\\\\mcp-server\\\\functions\\\\node_modules\\\\firebase-functions\\\\lib\\\\v2\\\\index.js\"}}"}}
[debug] [2025-07-30T13:17:33.389Z] [runtime-status] [632] Resolved module firebase-functions {"declared":true,"installed":true,"version":"6.4.0","resolution":"E:\\python_projects_factory\\animal\\mcp-server\\functions\\node_modules\\firebase-functions\\lib\\v2\\index.js"} {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[runtime-status] [632] Resolved module firebase-functions {\"declared\":true,\"installed\":true,\"version\":\"6.4.0\",\"resolution\":\"E:\\\\python_projects_factory\\\\animal\\\\mcp-server\\\\functions\\\\node_modules\\\\firebase-functions\\\\lib\\\\v2\\\\index.js\"}"}}
[debug] [2025-07-30T13:17:33.396Z] [runtime-status] [632] Resolved module firebase-admin {"declared":true,"installed":true,"version":"12.7.0","resolution":"E:\\python_projects_factory\\animal\\mcp-server\\functions\\node_modules\\firebase-admin\\lib\\index.js"} {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[runtime-status] [632] Resolved module firebase-admin {\"declared\":true,\"installed\":true,\"version\":\"12.7.0\",\"resolution\":\"E:\\\\python_projects_factory\\\\animal\\\\mcp-server\\\\functions\\\\node_modules\\\\firebase-admin\\\\lib\\\\index.js\"}"}}
[debug] [2025-07-30T13:17:33.399Z] [runtime-status] [632] Resolved module firebase-functions {"declared":true,"installed":true,"version":"6.4.0","resolution":"E:\\python_projects_factory\\animal\\mcp-server\\functions\\node_modules\\firebase-functions\\lib\\v2\\index.js"} {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[runtime-status] [632] Resolved module firebase-functions {\"declared\":true,\"installed\":true,\"version\":\"6.4.0\",\"resolution\":\"E:\\\\python_projects_factory\\\\animal\\\\mcp-server\\\\functions\\\\node_modules\\\\firebase-functions\\\\lib\\\\v2\\\\index.js\"}"}}
[debug] [2025-07-30T13:17:33.399Z] [runtime-status] [632] firebase-admin has been stubbed. {"adminResolution":{"declared":true,"installed":true,"version":"12.7.0","resolution":"E:\\python_projects_factory\\animal\\mcp-server\\functions\\node_modules\\firebase-admin\\lib\\index.js"}} {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[runtime-status] [632] firebase-admin has been stubbed. {\"adminResolution\":{\"declared\":true,\"installed\":true,\"version\":\"12.7.0\",\"resolution\":\"E:\\\\python_projects_factory\\\\animal\\\\mcp-server\\\\functions\\\\node_modules\\\\firebase-admin\\\\lib\\\\index.js\"}}"}}
[info] >  [dotenv@17.2.1] injecting env (0) from .env -- tip: ⚙️  load multiple .env files with { path: ['.env.local', '.env'] } {"user":"[dotenv@17.2.1] injecting env (0) from .env -- tip: ⚙️  load multiple .env files with { path: ['.env.local', '.env'] }","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m [dotenv@17.2.1] injecting env (0) from .env -- tip: ⚙️  load multiple .env files with { path: ['.env.local', '.env'] }"}}
*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
[debug] [2025-07-30T13:17:33.603Z] [runtime-status] [632] Functions runtime initialized. {"cwd":"E:\\python_projects_factory\\animal\\mcp-server\\functions","node_version":"22.14.0"} {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[runtime-status] [632] Functions runtime initialized. {\"cwd\":\"E:\\\\python_projects_factory\\\\animal\\\\mcp-server\\\\functions\",\"node_version\":\"22.14.0\"}"}}
[debug] [2025-07-30T13:17:33.604Z] [runtime-status] [632] Listening to port: \\?\pipe\fire_emu_01c2a3196d778cb0 {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[runtime-status] [632] Listening to port: \\\\?\\pipe\\fire_emu_01c2a3196d778cb0"}}
[debug] [2025-07-30T13:17:33.641Z] [worker-australia-southeast1-animalApp-ba127438-ffbd-44b0-b3d0-c441c528b36f]: IDLE {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[worker-australia-southeast1-animalApp-ba127438-ffbd-44b0-b3d0-c441c528b36f]: IDLE"}}
[debug] [2025-07-30T13:17:33.642Z] [worker-pool] submitRequest(triggerId=australia-southeast1-animalApp) {"metadata":{"emulator":{"name":"functions"},"message":"[worker-pool] submitRequest(triggerId=australia-southeast1-animalApp)"}}
[info] i  functions: Beginning execution of "australia-southeast1-animalApp" {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Beginning execution of \"australia-southeast1-animalApp\""}}
[debug] [2025-07-30T13:17:33.643Z] [worker-australia-southeast1-animalApp-ba127438-ffbd-44b0-b3d0-c441c528b36f]: BUSY {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[worker-australia-southeast1-animalApp-ba127438-ffbd-44b0-b3d0-c441c528b36f]: BUSY"}}
[debug] [2025-07-30T13:17:33.648Z] Finishing up request with event=pause {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=pause"}}
[info] i  functions: Finished "australia-southeast1-animalApp" in 5.226ms {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finished \"australia-southeast1-animalApp\" in 5.226ms"}}
[debug] [2025-07-30T13:17:33.649Z] [worker-australia-southeast1-animalApp-ba127438-ffbd-44b0-b3d0-c441c528b36f]: IDLE {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[worker-australia-southeast1-animalApp-ba127438-ffbd-44b0-b3d0-c441c528b36f]: IDLE"}}
[debug] [2025-07-30T13:17:33.649Z] Finishing up request with event=finish {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=finish"}}
[debug] [2025-07-30T13:17:33.649Z] Finishing up request with event=close {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=close"}}
[debug] [2025-07-30T13:17:33.649Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":[],"workRunningCount":0}
[debug] [2025-07-30T13:17:33.650Z] [work-queue] {"queuedWork":["/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat-2025-07-30T13:17:33.650Z"],"queueLength":1,"runningWork":[],"workRunningCount":0}
[debug] [2025-07-30T13:17:33.651Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":["/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat-2025-07-30T13:17:33.650Z"],"workRunningCount":1}
[debug] [2025-07-30T13:17:33.651Z] Accepted request POST /kissandost-9570f/australia-southeast1/animalApp/open-ai-chat --> australia-southeast1-animalApp
[debug] [2025-07-30T13:17:33.651Z] [functions] Runtime ready! Sending request! {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Runtime ready! Sending request!"}}
[debug] [2025-07-30T13:17:33.652Z] [functions] Got req.url=/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat, mapping to path=/open-ai-chat {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Got req.url=/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat, mapping to path=/open-ai-chat"}}
[debug] [2025-07-30T13:17:33.652Z] [worker-pool] submitRequest(triggerId=australia-southeast1-animalApp) {"metadata":{"emulator":{"name":"functions"},"message":"[worker-pool] submitRequest(triggerId=australia-southeast1-animalApp)"}}
[info] i  functions: Beginning execution of "australia-southeast1-animalApp" {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Beginning execution of \"australia-southeast1-animalApp\""}}
[debug] [2025-07-30T13:17:33.652Z] [worker-australia-southeast1-animalApp-ba127438-ffbd-44b0-b3d0-c441c528b36f]: BUSY {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[worker-australia-southeast1-animalApp-ba127438-ffbd-44b0-b3d0-c441c528b36f]: BUSY"}}
[info] >  🔁 Initializing routes... {"user":"🔁 Initializing routes...","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m 🔁 Initializing routes..."}}
[info] >  OpenAI client initialized successfully {"user":"OpenAI client initialized successfully","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m OpenAI client initialized successfully"}}
[warn] !  functions: The Cloud Firestore emulator is not running, so calls to Firestore will affect production. {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"The Cloud Firestore emulator is not running, so calls to Firestore will affect production."}}
[warn] !  functions: The Firebase Storage emulator is not running, so calls to Firebase Storage will affect production. {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"The Firebase Storage emulator is not running, so calls to Firebase Storage will affect production."}}
[info] >  🔁 Routes Initialized Successfully... {"user":"🔁 Routes Initialized Successfully...","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m 🔁 Routes Initialized Successfully..."}}
[info] >  ............................ { {"user":"............................ {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m ............................ {"}}
[info] >    id: 'obLA65ZT1JPo8Ytkrze6XAFBFE12', {"user":"  id: 'obLA65ZT1JPo8Ytkrze6XAFBFE12',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   id: 'obLA65ZT1JPo8Ytkrze6XAFBFE12',"}}
[info] >    email: '<EMAIL>', {"user":"  email: '<EMAIL>',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   email: '<EMAIL>',"}}
[info] >    role: 'owner', {"user":"  role: 'owner',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   role: 'owner',"}}
[info] >    language: 'en', {"user":"  language: 'en',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   language: 'en',"}}
[info] >    preferAudio: false, {"user":"  preferAudio: false,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   preferAudio: false,"}}
[info] >    offlineMode: false, {"user":"  offlineMode: false,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   offlineMode: false,"}}
[info] >    emailVerified: true, {"user":"  emailVerified: true,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   emailVerified: true,"}}
[info] >    createdAt: 1746969090959, {"user":"  createdAt: 1746969090959,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   createdAt: 1746969090959,"}}
[info] >    name: 'Dr. Javad Ahmed', {"user":"  name: 'Dr. Javad Ahmed',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   name: 'Dr. Javad Ahmed',"}}
[info] >    ownedFarms: [ 'ell4xZlOUxIwbSTdk4gp', 'YrwVdr1vr3KGqtXP3bqX' ], {"user":"  ownedFarms: [ 'ell4xZlOUxIwbSTdk4gp', 'YrwVdr1vr3KGqtXP3bqX' ],","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   ownedFarms: [ 'ell4xZlOUxIwbSTdk4gp', 'YrwVdr1vr3KGqtXP3bqX' ],"}}
[info] >    profilePicture: 'https://firebasestorage.googleapis.com/v0/b/kissandost-9570f.firebasestorage.app/o/profile-pictures%2FobLA65ZT1JPo8Ytkrze6XAFBFE12%2F1750768514392.jpg?alt=media&token=bb6acca1-9f21-4063-9db2-3b9994559e90', {"user":"  profilePicture: 'https://firebasestorage.googleapis.com/v0/b/kissandost-9570f.firebasestorage.app/o/profile-pictures%2FobLA65ZT1JPo8Ytkrze6XAFBFE12%2F1750768514392.jpg?alt=media&token=bb6acca1-9f21-4063-9db2-3b9994559e90',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   profilePicture: 'https://firebasestorage.googleapis.com/v0/b/kissandost-9570f.firebasestorage.app/o/profile-pictures%2FobLA65ZT1JPo8Ytkrze6XAFBFE12%2F1750768514392.jpg?alt=media&token=bb6acca1-9f21-4063-9db2-3b9994559e90',"}}
[info] >    lastLogin: Timestamp { _seconds: 1751376164, _nanoseconds: 17000000 }, {"user":"  lastLogin: Timestamp { _seconds: 1751376164, _nanoseconds: 17000000 },","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   lastLogin: Timestamp { _seconds: 1751376164, _nanoseconds: 17000000 },"}}
[info] >    updatedAt: 1753292227987, {"user":"  updatedAt: 1753292227987,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   updatedAt: 1753292227987,"}}
[info] >    assignedFarmIds: [ {"user":"  assignedFarmIds: [","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   assignedFarmIds: ["}}
[info] >      'ell4xZlOUxIwbSTdk4gp', 'YrwVdr1vr3KGqtXP3bqX', {"user":"    'ell4xZlOUxIwbSTdk4gp', 'YrwVdr1vr3KGqtXP3bqX',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'ell4xZlOUxIwbSTdk4gp', 'YrwVdr1vr3KGqtXP3bqX',"}}
[info] >      '7owH6eTfGSYqaRpgIsQE', 'icSTOLwTaC6WYgWjsgDk', {"user":"    '7owH6eTfGSYqaRpgIsQE', 'icSTOLwTaC6WYgWjsgDk',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     '7owH6eTfGSYqaRpgIsQE', 'icSTOLwTaC6WYgWjsgDk',"}}
[info] >      'BtniJTurDAn0saHHbEGF', 'wFSF3LbHQwPYy6A0pu94', {"user":"    'BtniJTurDAn0saHHbEGF', 'wFSF3LbHQwPYy6A0pu94',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'BtniJTurDAn0saHHbEGF', 'wFSF3LbHQwPYy6A0pu94',"}}
[info] >      'fNgkVoyHni01zmozumbK', 'C6FceATDCJxVtji42Q3D', {"user":"    'fNgkVoyHni01zmozumbK', 'C6FceATDCJxVtji42Q3D',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'fNgkVoyHni01zmozumbK', 'C6FceATDCJxVtji42Q3D',"}}
[info] >      'M5Pahb6Vi1rdq7KZB5LF', 'oXnGAP4V78vilhIVpIQN', {"user":"    'M5Pahb6Vi1rdq7KZB5LF', 'oXnGAP4V78vilhIVpIQN',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'M5Pahb6Vi1rdq7KZB5LF', 'oXnGAP4V78vilhIVpIQN',"}}
[info] >      'NpS3CdrpHvPZClMMDeFc', 'TlS4g9PWKN1wNBtjHEoq', {"user":"    'NpS3CdrpHvPZClMMDeFc', 'TlS4g9PWKN1wNBtjHEoq',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'NpS3CdrpHvPZClMMDeFc', 'TlS4g9PWKN1wNBtjHEoq',"}}
[info] >      'rb2n32HuXJlFOk44cAkJ', 'UWkNKuXggt70Oe4bkzcx', {"user":"    'rb2n32HuXJlFOk44cAkJ', 'UWkNKuXggt70Oe4bkzcx',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'rb2n32HuXJlFOk44cAkJ', 'UWkNKuXggt70Oe4bkzcx',"}}
[info] >      'jA9Azu4oZQidKpLjPZNu', 'FwtZUF6cO4YF0pVo7CZG', {"user":"    'jA9Azu4oZQidKpLjPZNu', 'FwtZUF6cO4YF0pVo7CZG',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'jA9Azu4oZQidKpLjPZNu', 'FwtZUF6cO4YF0pVo7CZG',"}}
[info] >      '7LzTfFyZUQXm6Cl9eKL1', 'u08I4BYxrWPuwv4Byqw7', {"user":"    '7LzTfFyZUQXm6Cl9eKL1', 'u08I4BYxrWPuwv4Byqw7',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     '7LzTfFyZUQXm6Cl9eKL1', 'u08I4BYxrWPuwv4Byqw7',"}}
[info] >      'reNZlIKsdFhJiKJOhC3E' {"user":"    'reNZlIKsdFhJiKJOhC3E'","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'reNZlIKsdFhJiKJOhC3E'"}}
[info] >    ] {"user":"  ]","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   ]"}}
[info] >  } {"user":"}","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m }"}}
[warn] !  External network resource requested!
   - URL: "https://api.openai.com/v1/chat/completions"
 - Be careful, this may be a production service. {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"External network resource requested!\n   - URL: \"https://api.openai.com/v1/chat/completions\"\n - Be careful, this may be a production service."}}
[info] >  🔍 REQUEST DEBUG: { {"user":"🔍 REQUEST DEBUG: {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m 🔍 REQUEST DEBUG: {"}}
[info] >    prompt: 'Add Birth Record record', {"user":"  prompt: 'Add Birth Record record',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   prompt: 'Add Birth Record record',"}}
[info] >    hasContext: false, {"user":"  hasContext: false,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   hasContext: false,"}}
[info] >    contextKeys: [], {"user":"  contextKeys: [],","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   contextKeys: [],"}}
[info] >    contextAction: undefined, {"user":"  contextAction: undefined,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   contextAction: undefined,"}}
[info] >    contextEditMode: undefined, {"user":"  contextEditMode: undefined,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   contextEditMode: undefined,"}}
[info] >    contextSelectedFarm: false {"user":"  contextSelectedFarm: false","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   contextSelectedFarm: false"}}
[info] >  } {"user":"}","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m }"}}
[info] >  🔍 REQUEST TYPE ANALYSIS: { {"user":"🔍 REQUEST TYPE ANALYSIS: {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m 🔍 REQUEST TYPE ANALYSIS: {"}}
[info] >    prompt: 'Add Birth Record record', {"user":"  prompt: 'Add Birth Record record',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   prompt: 'Add Birth Record record',"}}
[info] >    requestType: 'add_health_record', {"user":"  requestType: 'add_health_record',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   requestType: 'add_health_record',"}}
[info] >    hasContext: false, {"user":"  hasContext: false,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   hasContext: false,"}}
[info] >    contextAction: undefined {"user":"  contextAction: undefined","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   contextAction: undefined"}}
[info] >  } {"user":"}","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m }"}}
[info] >  📨 Context received: undefined {"user":"📨 Context received: undefined","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m 📨 Context received: undefined"}}
[info] >  📨 Context action: undefined {"user":"📨 Context action: undefined","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m 📨 Context action: undefined"}}
[info] >  📨 Context needsAnimalSelection: undefined {"user":"📨 Context needsAnimalSelection: undefined","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m 📨 Context needsAnimalSelection: undefined"}}
[info] >  AI Analysis Response: ```json {"user":"AI Analysis Response: ```json","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m AI Analysis Response: ```json"}}
[info] >  { {"user":"{","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m {"}}
[info] >    "animalName": null, {"user":"  \"animalName\": null,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   \"animalName\": null,"}}
[info] >    "recordType": "birth", {"user":"  \"recordType\": \"birth\",","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   \"recordType\": \"birth\","}}
[info] >    "recordOption": null, {"user":"  \"recordOption\": null,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   \"recordOption\": null,"}}
[info] >    "practitioner": "self", {"user":"  \"practitioner\": \"self\",","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   \"practitioner\": \"self\","}}
[info] >    "date": null, {"user":"  \"date\": null,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   \"date\": null,"}}
[info] >    "notes": null, {"user":"  \"notes\": null,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   \"notes\": null,"}}
[info] >    "action": "add", {"user":"  \"action\": \"add\",","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   \"action\": \"add\","}}
[info] >    "confidence": 90 {"user":"  \"confidence\": 90","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   \"confidence\": 90"}}
[info] >  } {"user":"}","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m }"}}
[info] >  ``` {"user":"```","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m ```"}}
[info] >  AI extracted health record info: { {"user":"AI extracted health record info: {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m AI extracted health record info: {"}}
[info] >    animalName: null, {"user":"  animalName: null,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   animalName: null,"}}
[info] >    recordType: 'birth', {"user":"  recordType: 'birth',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   recordType: 'birth',"}}
[info] >    recordOption: null, {"user":"  recordOption: null,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   recordOption: null,"}}
[info] >    practitioner: 'self', {"user":"  practitioner: 'self',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   practitioner: 'self',"}}
[info] >    date: null, {"user":"  date: null,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   date: null,"}}
[info] >    notes: null, {"user":"  notes: null,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   notes: null,"}}
[info] >    action: 'add', {"user":"  action: 'add',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   action: 'add',"}}
[info] >    confidence: 90 {"user":"  confidence: 90","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   confidence: 90"}}
[info] >  } {"user":"}","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m }"}}
[info] >  Using farm from dashboard for health record: Haven_View {"user":"Using farm from dashboard for health record: Haven_View","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m Using farm from dashboard for health record: Haven_View"}}
[info] >  Available animals for health record: 5 {"user":"Available animals for health record: 5","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m Available animals for health record: 5"}}
[info] >  Need to show record options for type: birth {"user":"Need to show record options for type: birth","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m Need to show record options for type: birth"}}
[debug] [2025-07-30T13:17:38.363Z] Finishing up request with event=pause {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=pause"}}
[info] i  functions: Finished "australia-southeast1-animalApp" in 4711.2159ms {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finished \"australia-southeast1-animalApp\" in 4711.2159ms"}}
[debug] [2025-07-30T13:17:38.364Z] [worker-australia-southeast1-animalApp-ba127438-ffbd-44b0-b3d0-c441c528b36f]: IDLE {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[worker-australia-southeast1-animalApp-ba127438-ffbd-44b0-b3d0-c441c528b36f]: IDLE"}}
[debug] [2025-07-30T13:17:38.365Z] Finishing up request with event=finish {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=finish"}}
[debug] [2025-07-30T13:17:38.365Z] Finishing up request with event=close {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=close"}}
[debug] [2025-07-30T13:17:38.365Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":[],"workRunningCount":0}
[debug] [2025-07-30T13:17:42.456Z] [work-queue] {"queuedWork":["/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat-2025-07-30T13:17:42.456Z"],"queueLength":1,"runningWork":[],"workRunningCount":0}
[debug] [2025-07-30T13:17:42.456Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":["/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat-2025-07-30T13:17:42.456Z"],"workRunningCount":1}
[debug] [2025-07-30T13:17:42.456Z] Accepted request OPTIONS /kissandost-9570f/australia-southeast1/animalApp/open-ai-chat --> australia-southeast1-animalApp
[debug] [2025-07-30T13:17:42.458Z] [functions] Runtime ready! Sending request! {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Runtime ready! Sending request!"}}
[debug] [2025-07-30T13:17:42.459Z] [functions] Got req.url=/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat, mapping to path=/open-ai-chat {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Got req.url=/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat, mapping to path=/open-ai-chat"}}
[debug] [2025-07-30T13:17:42.459Z] [worker-pool] submitRequest(triggerId=australia-southeast1-animalApp) {"metadata":{"emulator":{"name":"functions"},"message":"[worker-pool] submitRequest(triggerId=australia-southeast1-animalApp)"}}
[info] i  functions: Beginning execution of "australia-southeast1-animalApp" {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Beginning execution of \"australia-southeast1-animalApp\""}}
[debug] [2025-07-30T13:17:42.459Z] [worker-australia-southeast1-animalApp-ba127438-ffbd-44b0-b3d0-c441c528b36f]: BUSY {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[worker-australia-southeast1-animalApp-ba127438-ffbd-44b0-b3d0-c441c528b36f]: BUSY"}}
[debug] [2025-07-30T13:17:42.462Z] Finishing up request with event=pause {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=pause"}}
[info] i  functions: Finished "australia-southeast1-animalApp" in 2.6501ms {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finished \"australia-southeast1-animalApp\" in 2.6501ms"}}
[debug] [2025-07-30T13:17:42.463Z] [worker-australia-southeast1-animalApp-ba127438-ffbd-44b0-b3d0-c441c528b36f]: IDLE {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[worker-australia-southeast1-animalApp-ba127438-ffbd-44b0-b3d0-c441c528b36f]: IDLE"}}
[debug] [2025-07-30T13:17:42.463Z] Finishing up request with event=finish {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=finish"}}
[debug] [2025-07-30T13:17:42.463Z] Finishing up request with event=close {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=close"}}
[debug] [2025-07-30T13:17:42.463Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":[],"workRunningCount":0}
[debug] [2025-07-30T13:17:42.464Z] [work-queue] {"queuedWork":["/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat-2025-07-30T13:17:42.464Z"],"queueLength":1,"runningWork":[],"workRunningCount":0}
[debug] [2025-07-30T13:17:42.464Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":["/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat-2025-07-30T13:17:42.464Z"],"workRunningCount":1}
[debug] [2025-07-30T13:17:42.464Z] Accepted request POST /kissandost-9570f/australia-southeast1/animalApp/open-ai-chat --> australia-southeast1-animalApp
[debug] [2025-07-30T13:17:42.467Z] [functions] Runtime ready! Sending request! {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Runtime ready! Sending request!"}}
[debug] [2025-07-30T13:17:42.467Z] [functions] Got req.url=/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat, mapping to path=/open-ai-chat {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Got req.url=/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat, mapping to path=/open-ai-chat"}}
[debug] [2025-07-30T13:17:42.467Z] [worker-pool] submitRequest(triggerId=australia-southeast1-animalApp) {"metadata":{"emulator":{"name":"functions"},"message":"[worker-pool] submitRequest(triggerId=australia-southeast1-animalApp)"}}
[info] i  functions: Beginning execution of "australia-southeast1-animalApp" {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Beginning execution of \"australia-southeast1-animalApp\""}}
[debug] [2025-07-30T13:17:42.468Z] [worker-australia-southeast1-animalApp-ba127438-ffbd-44b0-b3d0-c441c528b36f]: BUSY {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[worker-australia-southeast1-animalApp-ba127438-ffbd-44b0-b3d0-c441c528b36f]: BUSY"}}
[info] >  ............................ { {"user":"............................ {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m ............................ {"}}
[info] >    id: 'obLA65ZT1JPo8Ytkrze6XAFBFE12', {"user":"  id: 'obLA65ZT1JPo8Ytkrze6XAFBFE12',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   id: 'obLA65ZT1JPo8Ytkrze6XAFBFE12',"}}
[info] >    email: '<EMAIL>', {"user":"  email: '<EMAIL>',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   email: '<EMAIL>',"}}
[info] >    role: 'owner', {"user":"  role: 'owner',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   role: 'owner',"}}
[info] >    language: 'en', {"user":"  language: 'en',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   language: 'en',"}}
[info] >    preferAudio: false, {"user":"  preferAudio: false,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   preferAudio: false,"}}
[info] >    offlineMode: false, {"user":"  offlineMode: false,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   offlineMode: false,"}}
[info] >    emailVerified: true, {"user":"  emailVerified: true,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   emailVerified: true,"}}
[info] >    createdAt: 1746969090959, {"user":"  createdAt: 1746969090959,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   createdAt: 1746969090959,"}}
[info] >    name: 'Dr. Javad Ahmed', {"user":"  name: 'Dr. Javad Ahmed',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   name: 'Dr. Javad Ahmed',"}}
[info] >    ownedFarms: [ 'ell4xZlOUxIwbSTdk4gp', 'YrwVdr1vr3KGqtXP3bqX' ], {"user":"  ownedFarms: [ 'ell4xZlOUxIwbSTdk4gp', 'YrwVdr1vr3KGqtXP3bqX' ],","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   ownedFarms: [ 'ell4xZlOUxIwbSTdk4gp', 'YrwVdr1vr3KGqtXP3bqX' ],"}}
[info] >    profilePicture: 'https://firebasestorage.googleapis.com/v0/b/kissandost-9570f.firebasestorage.app/o/profile-pictures%2FobLA65ZT1JPo8Ytkrze6XAFBFE12%2F1750768514392.jpg?alt=media&token=bb6acca1-9f21-4063-9db2-3b9994559e90', {"user":"  profilePicture: 'https://firebasestorage.googleapis.com/v0/b/kissandost-9570f.firebasestorage.app/o/profile-pictures%2FobLA65ZT1JPo8Ytkrze6XAFBFE12%2F1750768514392.jpg?alt=media&token=bb6acca1-9f21-4063-9db2-3b9994559e90',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   profilePicture: 'https://firebasestorage.googleapis.com/v0/b/kissandost-9570f.firebasestorage.app/o/profile-pictures%2FobLA65ZT1JPo8Ytkrze6XAFBFE12%2F1750768514392.jpg?alt=media&token=bb6acca1-9f21-4063-9db2-3b9994559e90',"}}
[info] >    lastLogin: Timestamp { _seconds: 1751376164, _nanoseconds: 17000000 }, {"user":"  lastLogin: Timestamp { _seconds: 1751376164, _nanoseconds: 17000000 },","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   lastLogin: Timestamp { _seconds: 1751376164, _nanoseconds: 17000000 },"}}
[info] >    updatedAt: 1753292227987, {"user":"  updatedAt: 1753292227987,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   updatedAt: 1753292227987,"}}
[info] >    assignedFarmIds: [ {"user":"  assignedFarmIds: [","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   assignedFarmIds: ["}}
[info] >      'ell4xZlOUxIwbSTdk4gp', 'YrwVdr1vr3KGqtXP3bqX', {"user":"    'ell4xZlOUxIwbSTdk4gp', 'YrwVdr1vr3KGqtXP3bqX',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'ell4xZlOUxIwbSTdk4gp', 'YrwVdr1vr3KGqtXP3bqX',"}}
[info] >      '7owH6eTfGSYqaRpgIsQE', 'icSTOLwTaC6WYgWjsgDk', {"user":"    '7owH6eTfGSYqaRpgIsQE', 'icSTOLwTaC6WYgWjsgDk',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     '7owH6eTfGSYqaRpgIsQE', 'icSTOLwTaC6WYgWjsgDk',"}}
[info] >      'BtniJTurDAn0saHHbEGF', 'wFSF3LbHQwPYy6A0pu94', {"user":"    'BtniJTurDAn0saHHbEGF', 'wFSF3LbHQwPYy6A0pu94',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'BtniJTurDAn0saHHbEGF', 'wFSF3LbHQwPYy6A0pu94',"}}
[info] >      'fNgkVoyHni01zmozumbK', 'C6FceATDCJxVtji42Q3D', {"user":"    'fNgkVoyHni01zmozumbK', 'C6FceATDCJxVtji42Q3D',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'fNgkVoyHni01zmozumbK', 'C6FceATDCJxVtji42Q3D',"}}
[info] >      'M5Pahb6Vi1rdq7KZB5LF', 'oXnGAP4V78vilhIVpIQN', {"user":"    'M5Pahb6Vi1rdq7KZB5LF', 'oXnGAP4V78vilhIVpIQN',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'M5Pahb6Vi1rdq7KZB5LF', 'oXnGAP4V78vilhIVpIQN',"}}
[info] >      'NpS3CdrpHvPZClMMDeFc', 'TlS4g9PWKN1wNBtjHEoq', {"user":"    'NpS3CdrpHvPZClMMDeFc', 'TlS4g9PWKN1wNBtjHEoq',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'NpS3CdrpHvPZClMMDeFc', 'TlS4g9PWKN1wNBtjHEoq',"}}
[info] >      'rb2n32HuXJlFOk44cAkJ', 'UWkNKuXggt70Oe4bkzcx', {"user":"    'rb2n32HuXJlFOk44cAkJ', 'UWkNKuXggt70Oe4bkzcx',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'rb2n32HuXJlFOk44cAkJ', 'UWkNKuXggt70Oe4bkzcx',"}}
[info] >      'jA9Azu4oZQidKpLjPZNu', 'FwtZUF6cO4YF0pVo7CZG', {"user":"    'jA9Azu4oZQidKpLjPZNu', 'FwtZUF6cO4YF0pVo7CZG',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'jA9Azu4oZQidKpLjPZNu', 'FwtZUF6cO4YF0pVo7CZG',"}}
[info] >      '7LzTfFyZUQXm6Cl9eKL1', 'u08I4BYxrWPuwv4Byqw7', {"user":"    '7LzTfFyZUQXm6Cl9eKL1', 'u08I4BYxrWPuwv4Byqw7',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     '7LzTfFyZUQXm6Cl9eKL1', 'u08I4BYxrWPuwv4Byqw7',"}}
[info] >      'reNZlIKsdFhJiKJOhC3E' {"user":"    'reNZlIKsdFhJiKJOhC3E'","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'reNZlIKsdFhJiKJOhC3E'"}}
[info] >    ] {"user":"  ]","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   ]"}}
[info] >  } {"user":"}","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m }"}}
[info] >  🔍 REQUEST DEBUG: { {"user":"🔍 REQUEST DEBUG: {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m 🔍 REQUEST DEBUG: {"}}
[info] >    prompt: '2', {"user":"  prompt: '2',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   prompt: '2',"}}
[info] >    hasContext: false, {"user":"  hasContext: false,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   hasContext: false,"}}
[info] >    contextKeys: [], {"user":"  contextKeys: [],","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   contextKeys: [],"}}
[info] >    contextAction: undefined, {"user":"  contextAction: undefined,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   contextAction: undefined,"}}
[info] >    contextEditMode: undefined, {"user":"  contextEditMode: undefined,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   contextEditMode: undefined,"}}
[info] >    contextSelectedFarm: false {"user":"  contextSelectedFarm: false","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   contextSelectedFarm: false"}}
[info] >  } {"user":"}","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m }"}}
[info] >  🔍 REQUEST TYPE ANALYSIS: { {"user":"🔍 REQUEST TYPE ANALYSIS: {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m 🔍 REQUEST TYPE ANALYSIS: {"}}
[info] >    prompt: '2', {"user":"  prompt: '2',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   prompt: '2',"}}
[info] >    requestType: 'unknown', {"user":"  requestType: 'unknown',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   requestType: 'unknown',"}}
[info] >    hasContext: false, {"user":"  hasContext: false,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   hasContext: false,"}}
[info] >    contextAction: undefined {"user":"  contextAction: undefined","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   contextAction: undefined"}}
[info] >  } {"user":"}","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m }"}}
[info] >  📨 Context received: null {"user":"📨 Context received: null","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m 📨 Context received: null"}}
[info] >  📨 Context action: undefined {"user":"📨 Context action: undefined","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m 📨 Context action: undefined"}}
[info] >  📨 Context needsAnimalSelection: undefined {"user":"📨 Context needsAnimalSelection: undefined","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m 📨 Context needsAnimalSelection: undefined"}}
[info] >  Chat response: Hello! How can I assist you today? {"user":"Chat response: Hello! How can I assist you today?","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m Chat response: Hello! How can I assist you today?"}}
[debug] [2025-07-30T13:17:43.591Z] Finishing up request with event=pause {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=pause"}}
[info] i  functions: Finished "australia-southeast1-animalApp" in 1123.2046ms {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finished \"australia-southeast1-animalApp\" in 1123.2046ms"}}
[debug] [2025-07-30T13:17:43.592Z] [worker-australia-southeast1-animalApp-ba127438-ffbd-44b0-b3d0-c441c528b36f]: IDLE {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[worker-australia-southeast1-animalApp-ba127438-ffbd-44b0-b3d0-c441c528b36f]: IDLE"}}
[debug] [2025-07-30T13:17:43.592Z] Finishing up request with event=finish {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=finish"}}
[debug] [2025-07-30T13:17:43.592Z] Finishing up request with event=close {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=close"}}
[debug] [2025-07-30T13:17:43.592Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":[],"workRunningCount":0}
[debug] [2025-07-30T13:27:00.339Z] [work-queue] {"queuedWork":["/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat-2025-07-30T13:27:00.339Z"],"queueLength":1,"runningWork":[],"workRunningCount":0}
[debug] [2025-07-30T13:27:00.339Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":["/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat-2025-07-30T13:27:00.339Z"],"workRunningCount":1}
[debug] [2025-07-30T13:27:00.339Z] Accepted request OPTIONS /kissandost-9570f/australia-southeast1/animalApp/open-ai-chat --> australia-southeast1-animalApp
[debug] [2025-07-30T13:27:00.342Z] [functions] Runtime ready! Sending request! {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Runtime ready! Sending request!"}}
[debug] [2025-07-30T13:27:00.342Z] [functions] Got req.url=/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat, mapping to path=/open-ai-chat {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Got req.url=/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat, mapping to path=/open-ai-chat"}}
[debug] [2025-07-30T13:27:00.343Z] [worker-pool] submitRequest(triggerId=australia-southeast1-animalApp) {"metadata":{"emulator":{"name":"functions"},"message":"[worker-pool] submitRequest(triggerId=australia-southeast1-animalApp)"}}
[info] i  functions: Beginning execution of "australia-southeast1-animalApp" {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Beginning execution of \"australia-southeast1-animalApp\""}}
[debug] [2025-07-30T13:27:00.343Z] [worker-australia-southeast1-animalApp-ba127438-ffbd-44b0-b3d0-c441c528b36f]: BUSY {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[worker-australia-southeast1-animalApp-ba127438-ffbd-44b0-b3d0-c441c528b36f]: BUSY"}}
[debug] [2025-07-30T13:27:00.347Z] Finishing up request with event=pause {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=pause"}}
[info] i  functions: Finished "australia-southeast1-animalApp" in 3.5295ms {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finished \"australia-southeast1-animalApp\" in 3.5295ms"}}
[debug] [2025-07-30T13:27:00.352Z] [worker-australia-southeast1-animalApp-ba127438-ffbd-44b0-b3d0-c441c528b36f]: IDLE {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[worker-australia-southeast1-animalApp-ba127438-ffbd-44b0-b3d0-c441c528b36f]: IDLE"}}
[debug] [2025-07-30T13:27:00.352Z] Finishing up request with event=finish {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=finish"}}
[debug] [2025-07-30T13:27:00.352Z] Finishing up request with event=close {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=close"}}
[debug] [2025-07-30T13:27:00.352Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":[],"workRunningCount":0}
[debug] [2025-07-30T13:27:00.353Z] [work-queue] {"queuedWork":["/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat-2025-07-30T13:27:00.353Z"],"queueLength":1,"runningWork":[],"workRunningCount":0}
[debug] [2025-07-30T13:27:00.353Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":["/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat-2025-07-30T13:27:00.353Z"],"workRunningCount":1}
[debug] [2025-07-30T13:27:00.353Z] Accepted request POST /kissandost-9570f/australia-southeast1/animalApp/open-ai-chat --> australia-southeast1-animalApp
[debug] [2025-07-30T13:27:00.355Z] [functions] Runtime ready! Sending request! {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Runtime ready! Sending request!"}}
[debug] [2025-07-30T13:27:00.355Z] [functions] Got req.url=/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat, mapping to path=/open-ai-chat {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Got req.url=/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat, mapping to path=/open-ai-chat"}}
[debug] [2025-07-30T13:27:00.356Z] [worker-pool] submitRequest(triggerId=australia-southeast1-animalApp) {"metadata":{"emulator":{"name":"functions"},"message":"[worker-pool] submitRequest(triggerId=australia-southeast1-animalApp)"}}
[info] i  functions: Beginning execution of "australia-southeast1-animalApp" {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Beginning execution of \"australia-southeast1-animalApp\""}}
[debug] [2025-07-30T13:27:00.358Z] [worker-australia-southeast1-animalApp-ba127438-ffbd-44b0-b3d0-c441c528b36f]: BUSY {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[worker-australia-southeast1-animalApp-ba127438-ffbd-44b0-b3d0-c441c528b36f]: BUSY"}}
[info] >  ............................ { {"user":"............................ {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m ............................ {"}}
[info] >    id: 'obLA65ZT1JPo8Ytkrze6XAFBFE12', {"user":"  id: 'obLA65ZT1JPo8Ytkrze6XAFBFE12',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   id: 'obLA65ZT1JPo8Ytkrze6XAFBFE12',"}}
[info] >    email: '<EMAIL>', {"user":"  email: '<EMAIL>',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   email: '<EMAIL>',"}}
[info] >    role: 'owner', {"user":"  role: 'owner',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   role: 'owner',"}}
[info] >    language: 'en', {"user":"  language: 'en',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   language: 'en',"}}
[info] >    preferAudio: false, {"user":"  preferAudio: false,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   preferAudio: false,"}}
[info] >    offlineMode: false, {"user":"  offlineMode: false,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   offlineMode: false,"}}
[info] >    emailVerified: true, {"user":"  emailVerified: true,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   emailVerified: true,"}}
[info] >    createdAt: 1746969090959, {"user":"  createdAt: 1746969090959,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   createdAt: 1746969090959,"}}
[info] >    name: 'Dr. Javad Ahmed', {"user":"  name: 'Dr. Javad Ahmed',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   name: 'Dr. Javad Ahmed',"}}
[info] >    ownedFarms: [ 'ell4xZlOUxIwbSTdk4gp', 'YrwVdr1vr3KGqtXP3bqX' ], {"user":"  ownedFarms: [ 'ell4xZlOUxIwbSTdk4gp', 'YrwVdr1vr3KGqtXP3bqX' ],","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   ownedFarms: [ 'ell4xZlOUxIwbSTdk4gp', 'YrwVdr1vr3KGqtXP3bqX' ],"}}
[info] >    profilePicture: 'https://firebasestorage.googleapis.com/v0/b/kissandost-9570f.firebasestorage.app/o/profile-pictures%2FobLA65ZT1JPo8Ytkrze6XAFBFE12%2F1750768514392.jpg?alt=media&token=bb6acca1-9f21-4063-9db2-3b9994559e90', {"user":"  profilePicture: 'https://firebasestorage.googleapis.com/v0/b/kissandost-9570f.firebasestorage.app/o/profile-pictures%2FobLA65ZT1JPo8Ytkrze6XAFBFE12%2F1750768514392.jpg?alt=media&token=bb6acca1-9f21-4063-9db2-3b9994559e90',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   profilePicture: 'https://firebasestorage.googleapis.com/v0/b/kissandost-9570f.firebasestorage.app/o/profile-pictures%2FobLA65ZT1JPo8Ytkrze6XAFBFE12%2F1750768514392.jpg?alt=media&token=bb6acca1-9f21-4063-9db2-3b9994559e90',"}}
[info] >    lastLogin: Timestamp { _seconds: 1751376164, _nanoseconds: 17000000 }, {"user":"  lastLogin: Timestamp { _seconds: 1751376164, _nanoseconds: 17000000 },","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   lastLogin: Timestamp { _seconds: 1751376164, _nanoseconds: 17000000 },"}}
[info] >    updatedAt: 1753292227987, {"user":"  updatedAt: 1753292227987,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   updatedAt: 1753292227987,"}}
[info] >    assignedFarmIds: [ {"user":"  assignedFarmIds: [","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   assignedFarmIds: ["}}
[info] >      'ell4xZlOUxIwbSTdk4gp', 'YrwVdr1vr3KGqtXP3bqX', {"user":"    'ell4xZlOUxIwbSTdk4gp', 'YrwVdr1vr3KGqtXP3bqX',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'ell4xZlOUxIwbSTdk4gp', 'YrwVdr1vr3KGqtXP3bqX',"}}
[info] >      '7owH6eTfGSYqaRpgIsQE', 'icSTOLwTaC6WYgWjsgDk', {"user":"    '7owH6eTfGSYqaRpgIsQE', 'icSTOLwTaC6WYgWjsgDk',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     '7owH6eTfGSYqaRpgIsQE', 'icSTOLwTaC6WYgWjsgDk',"}}
[info] >      'BtniJTurDAn0saHHbEGF', 'wFSF3LbHQwPYy6A0pu94', {"user":"    'BtniJTurDAn0saHHbEGF', 'wFSF3LbHQwPYy6A0pu94',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'BtniJTurDAn0saHHbEGF', 'wFSF3LbHQwPYy6A0pu94',"}}
[info] >      'fNgkVoyHni01zmozumbK', 'C6FceATDCJxVtji42Q3D', {"user":"    'fNgkVoyHni01zmozumbK', 'C6FceATDCJxVtji42Q3D',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'fNgkVoyHni01zmozumbK', 'C6FceATDCJxVtji42Q3D',"}}
[info] >      'M5Pahb6Vi1rdq7KZB5LF', 'oXnGAP4V78vilhIVpIQN', {"user":"    'M5Pahb6Vi1rdq7KZB5LF', 'oXnGAP4V78vilhIVpIQN',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'M5Pahb6Vi1rdq7KZB5LF', 'oXnGAP4V78vilhIVpIQN',"}}
[info] >      'NpS3CdrpHvPZClMMDeFc', 'TlS4g9PWKN1wNBtjHEoq', {"user":"    'NpS3CdrpHvPZClMMDeFc', 'TlS4g9PWKN1wNBtjHEoq',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'NpS3CdrpHvPZClMMDeFc', 'TlS4g9PWKN1wNBtjHEoq',"}}
[info] >      'rb2n32HuXJlFOk44cAkJ', 'UWkNKuXggt70Oe4bkzcx', {"user":"    'rb2n32HuXJlFOk44cAkJ', 'UWkNKuXggt70Oe4bkzcx',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'rb2n32HuXJlFOk44cAkJ', 'UWkNKuXggt70Oe4bkzcx',"}}
[info] >      'jA9Azu4oZQidKpLjPZNu', 'FwtZUF6cO4YF0pVo7CZG', {"user":"    'jA9Azu4oZQidKpLjPZNu', 'FwtZUF6cO4YF0pVo7CZG',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'jA9Azu4oZQidKpLjPZNu', 'FwtZUF6cO4YF0pVo7CZG',"}}
[info] >      '7LzTfFyZUQXm6Cl9eKL1', 'u08I4BYxrWPuwv4Byqw7', {"user":"    '7LzTfFyZUQXm6Cl9eKL1', 'u08I4BYxrWPuwv4Byqw7',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     '7LzTfFyZUQXm6Cl9eKL1', 'u08I4BYxrWPuwv4Byqw7',"}}
[info] >      'reNZlIKsdFhJiKJOhC3E' {"user":"    'reNZlIKsdFhJiKJOhC3E'","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'reNZlIKsdFhJiKJOhC3E'"}}
[info] >    ] {"user":"  ]","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   ]"}}
[info] >  } {"user":"}","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m }"}}
[info] >  🔍 REQUEST DEBUG: { {"user":"🔍 REQUEST DEBUG: {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m 🔍 REQUEST DEBUG: {"}}
[info] >    prompt: 'Add Birth Record record', {"user":"  prompt: 'Add Birth Record record',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   prompt: 'Add Birth Record record',"}}
[info] >    hasContext: false, {"user":"  hasContext: false,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   hasContext: false,"}}
[info] >    contextKeys: [], {"user":"  contextKeys: [],","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   contextKeys: [],"}}
[info] >    contextAction: undefined, {"user":"  contextAction: undefined,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   contextAction: undefined,"}}
[info] >    contextEditMode: undefined, {"user":"  contextEditMode: undefined,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   contextEditMode: undefined,"}}
[info] >    contextSelectedFarm: false {"user":"  contextSelectedFarm: false","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   contextSelectedFarm: false"}}
[info] >  } {"user":"}","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m }"}}
[info] >  🔍 REQUEST TYPE ANALYSIS: { {"user":"🔍 REQUEST TYPE ANALYSIS: {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m 🔍 REQUEST TYPE ANALYSIS: {"}}
[info] >    prompt: 'Add Birth Record record', {"user":"  prompt: 'Add Birth Record record',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   prompt: 'Add Birth Record record',"}}
[info] >    requestType: 'add_health_record', {"user":"  requestType: 'add_health_record',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   requestType: 'add_health_record',"}}
[info] >    hasContext: false, {"user":"  hasContext: false,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   hasContext: false,"}}
[info] >    contextAction: undefined {"user":"  contextAction: undefined","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   contextAction: undefined"}}
[info] >  } {"user":"}","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m }"}}
[info] >  📨 Context received: undefined {"user":"📨 Context received: undefined","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m 📨 Context received: undefined"}}
[info] >  📨 Context action: undefined {"user":"📨 Context action: undefined","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m 📨 Context action: undefined"}}
[info] >  📨 Context needsAnimalSelection: undefined {"user":"📨 Context needsAnimalSelection: undefined","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m 📨 Context needsAnimalSelection: undefined"}}
[info] >  AI Analysis Response: ```json {"user":"AI Analysis Response: ```json","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m AI Analysis Response: ```json"}}
[info] >  { {"user":"{","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m {"}}
[info] >    "animalName": null, {"user":"  \"animalName\": null,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   \"animalName\": null,"}}
[info] >    "recordType": "birth", {"user":"  \"recordType\": \"birth\",","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   \"recordType\": \"birth\","}}
[info] >    "recordOption": null, {"user":"  \"recordOption\": null,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   \"recordOption\": null,"}}
[info] >    "practitioner": "self", {"user":"  \"practitioner\": \"self\",","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   \"practitioner\": \"self\","}}
[info] >    "date": null, {"user":"  \"date\": null,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   \"date\": null,"}}
[info] >    "notes": null, {"user":"  \"notes\": null,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   \"notes\": null,"}}
[info] >    "action": "add", {"user":"  \"action\": \"add\",","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   \"action\": \"add\","}}
[info] >    "confidence": 90 {"user":"  \"confidence\": 90","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   \"confidence\": 90"}}
[info] >  } {"user":"}","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m }"}}
[info] >  ``` {"user":"```","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m ```"}}
[info] >  AI extracted health record info: { {"user":"AI extracted health record info: {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m AI extracted health record info: {"}}
[info] >    animalName: null, {"user":"  animalName: null,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   animalName: null,"}}
[info] >    recordType: 'birth', {"user":"  recordType: 'birth',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   recordType: 'birth',"}}
[info] >    recordOption: null, {"user":"  recordOption: null,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   recordOption: null,"}}
[info] >    practitioner: 'self', {"user":"  practitioner: 'self',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   practitioner: 'self',"}}
[info] >    date: null, {"user":"  date: null,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   date: null,"}}
[info] >    notes: null, {"user":"  notes: null,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   notes: null,"}}
[info] >    action: 'add', {"user":"  action: 'add',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   action: 'add',"}}
[info] >    confidence: 90 {"user":"  confidence: 90","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   confidence: 90"}}
[info] >  } {"user":"}","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m }"}}
[info] >  Using farm from dashboard for health record: Haven_View {"user":"Using farm from dashboard for health record: Haven_View","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m Using farm from dashboard for health record: Haven_View"}}
[info] >  Available animals for health record: 5 {"user":"Available animals for health record: 5","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m Available animals for health record: 5"}}
[info] >  Need to show record options for type: birth {"user":"Need to show record options for type: birth","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m Need to show record options for type: birth"}}
[debug] [2025-07-30T13:27:04.217Z] Finishing up request with event=pause {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=pause"}}
[info] i  functions: Finished "australia-southeast1-animalApp" in 3859.4210000000003ms {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finished \"australia-southeast1-animalApp\" in 3859.4210000000003ms"}}
[debug] [2025-07-30T13:27:04.217Z] [worker-australia-southeast1-animalApp-ba127438-ffbd-44b0-b3d0-c441c528b36f]: IDLE {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[worker-australia-southeast1-animalApp-ba127438-ffbd-44b0-b3d0-c441c528b36f]: IDLE"}}
[debug] [2025-07-30T13:27:04.218Z] Finishing up request with event=finish {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=finish"}}
[debug] [2025-07-30T13:27:04.218Z] Finishing up request with event=close {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=close"}}
[debug] [2025-07-30T13:27:04.218Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":[],"workRunningCount":0}
[debug] [2025-07-30T13:27:10.108Z] [work-queue] {"queuedWork":["/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat-2025-07-30T13:27:10.108Z"],"queueLength":1,"runningWork":[],"workRunningCount":0}
[debug] [2025-07-30T13:27:10.108Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":["/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat-2025-07-30T13:27:10.108Z"],"workRunningCount":1}
[debug] [2025-07-30T13:27:10.108Z] Accepted request OPTIONS /kissandost-9570f/australia-southeast1/animalApp/open-ai-chat --> australia-southeast1-animalApp
[debug] [2025-07-30T13:27:10.110Z] [functions] Runtime ready! Sending request! {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Runtime ready! Sending request!"}}
[debug] [2025-07-30T13:27:10.110Z] [functions] Got req.url=/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat, mapping to path=/open-ai-chat {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Got req.url=/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat, mapping to path=/open-ai-chat"}}
[debug] [2025-07-30T13:27:10.111Z] [worker-pool] submitRequest(triggerId=australia-southeast1-animalApp) {"metadata":{"emulator":{"name":"functions"},"message":"[worker-pool] submitRequest(triggerId=australia-southeast1-animalApp)"}}
[info] i  functions: Beginning execution of "australia-southeast1-animalApp" {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Beginning execution of \"australia-southeast1-animalApp\""}}
[debug] [2025-07-30T13:27:10.111Z] [worker-australia-southeast1-animalApp-ba127438-ffbd-44b0-b3d0-c441c528b36f]: BUSY {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[worker-australia-southeast1-animalApp-ba127438-ffbd-44b0-b3d0-c441c528b36f]: BUSY"}}
[debug] [2025-07-30T13:27:10.113Z] Finishing up request with event=pause {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=pause"}}
[info] i  functions: Finished "australia-southeast1-animalApp" in 2.1479ms {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finished \"australia-southeast1-animalApp\" in 2.1479ms"}}
[debug] [2025-07-30T13:27:10.114Z] [worker-australia-southeast1-animalApp-ba127438-ffbd-44b0-b3d0-c441c528b36f]: IDLE {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[worker-australia-southeast1-animalApp-ba127438-ffbd-44b0-b3d0-c441c528b36f]: IDLE"}}
[debug] [2025-07-30T13:27:10.115Z] Finishing up request with event=finish {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=finish"}}
[debug] [2025-07-30T13:27:10.115Z] Finishing up request with event=close {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=close"}}
[debug] [2025-07-30T13:27:10.115Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":[],"workRunningCount":0}
[debug] [2025-07-30T13:27:10.115Z] [work-queue] {"queuedWork":["/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat-2025-07-30T13:27:10.115Z"],"queueLength":1,"runningWork":[],"workRunningCount":0}
[debug] [2025-07-30T13:27:10.115Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":["/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat-2025-07-30T13:27:10.115Z"],"workRunningCount":1}
[debug] [2025-07-30T13:27:10.116Z] Accepted request POST /kissandost-9570f/australia-southeast1/animalApp/open-ai-chat --> australia-southeast1-animalApp
[debug] [2025-07-30T13:27:10.118Z] [functions] Runtime ready! Sending request! {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Runtime ready! Sending request!"}}
[debug] [2025-07-30T13:27:10.118Z] [functions] Got req.url=/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat, mapping to path=/open-ai-chat {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Got req.url=/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat, mapping to path=/open-ai-chat"}}
[debug] [2025-07-30T13:27:10.118Z] [worker-pool] submitRequest(triggerId=australia-southeast1-animalApp) {"metadata":{"emulator":{"name":"functions"},"message":"[worker-pool] submitRequest(triggerId=australia-southeast1-animalApp)"}}
[info] i  functions: Beginning execution of "australia-southeast1-animalApp" {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Beginning execution of \"australia-southeast1-animalApp\""}}
[debug] [2025-07-30T13:27:10.120Z] [worker-australia-southeast1-animalApp-ba127438-ffbd-44b0-b3d0-c441c528b36f]: BUSY {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[worker-australia-southeast1-animalApp-ba127438-ffbd-44b0-b3d0-c441c528b36f]: BUSY"}}
[info] >  ............................ { {"user":"............................ {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m ............................ {"}}
[info] >    id: 'obLA65ZT1JPo8Ytkrze6XAFBFE12', {"user":"  id: 'obLA65ZT1JPo8Ytkrze6XAFBFE12',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   id: 'obLA65ZT1JPo8Ytkrze6XAFBFE12',"}}
[info] >    email: '<EMAIL>', {"user":"  email: '<EMAIL>',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   email: '<EMAIL>',"}}
[info] >    role: 'owner', {"user":"  role: 'owner',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   role: 'owner',"}}
[info] >    language: 'en', {"user":"  language: 'en',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   language: 'en',"}}
[info] >    preferAudio: false, {"user":"  preferAudio: false,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   preferAudio: false,"}}
[info] >    offlineMode: false, {"user":"  offlineMode: false,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   offlineMode: false,"}}
[info] >    emailVerified: true, {"user":"  emailVerified: true,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   emailVerified: true,"}}
[info] >    createdAt: 1746969090959, {"user":"  createdAt: 1746969090959,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   createdAt: 1746969090959,"}}
[info] >    name: 'Dr. Javad Ahmed', {"user":"  name: 'Dr. Javad Ahmed',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   name: 'Dr. Javad Ahmed',"}}
[info] >    ownedFarms: [ 'ell4xZlOUxIwbSTdk4gp', 'YrwVdr1vr3KGqtXP3bqX' ], {"user":"  ownedFarms: [ 'ell4xZlOUxIwbSTdk4gp', 'YrwVdr1vr3KGqtXP3bqX' ],","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   ownedFarms: [ 'ell4xZlOUxIwbSTdk4gp', 'YrwVdr1vr3KGqtXP3bqX' ],"}}
[info] >    profilePicture: 'https://firebasestorage.googleapis.com/v0/b/kissandost-9570f.firebasestorage.app/o/profile-pictures%2FobLA65ZT1JPo8Ytkrze6XAFBFE12%2F1750768514392.jpg?alt=media&token=bb6acca1-9f21-4063-9db2-3b9994559e90', {"user":"  profilePicture: 'https://firebasestorage.googleapis.com/v0/b/kissandost-9570f.firebasestorage.app/o/profile-pictures%2FobLA65ZT1JPo8Ytkrze6XAFBFE12%2F1750768514392.jpg?alt=media&token=bb6acca1-9f21-4063-9db2-3b9994559e90',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   profilePicture: 'https://firebasestorage.googleapis.com/v0/b/kissandost-9570f.firebasestorage.app/o/profile-pictures%2FobLA65ZT1JPo8Ytkrze6XAFBFE12%2F1750768514392.jpg?alt=media&token=bb6acca1-9f21-4063-9db2-3b9994559e90',"}}
[info] >    lastLogin: Timestamp { _seconds: 1751376164, _nanoseconds: 17000000 }, {"user":"  lastLogin: Timestamp { _seconds: 1751376164, _nanoseconds: 17000000 },","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   lastLogin: Timestamp { _seconds: 1751376164, _nanoseconds: 17000000 },"}}
[info] >    updatedAt: 1753292227987, {"user":"  updatedAt: 1753292227987,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   updatedAt: 1753292227987,"}}
[info] >    assignedFarmIds: [ {"user":"  assignedFarmIds: [","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   assignedFarmIds: ["}}
[info] >      'ell4xZlOUxIwbSTdk4gp', 'YrwVdr1vr3KGqtXP3bqX', {"user":"    'ell4xZlOUxIwbSTdk4gp', 'YrwVdr1vr3KGqtXP3bqX',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'ell4xZlOUxIwbSTdk4gp', 'YrwVdr1vr3KGqtXP3bqX',"}}
[info] >      '7owH6eTfGSYqaRpgIsQE', 'icSTOLwTaC6WYgWjsgDk', {"user":"    '7owH6eTfGSYqaRpgIsQE', 'icSTOLwTaC6WYgWjsgDk',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     '7owH6eTfGSYqaRpgIsQE', 'icSTOLwTaC6WYgWjsgDk',"}}
[info] >      'BtniJTurDAn0saHHbEGF', 'wFSF3LbHQwPYy6A0pu94', {"user":"    'BtniJTurDAn0saHHbEGF', 'wFSF3LbHQwPYy6A0pu94',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'BtniJTurDAn0saHHbEGF', 'wFSF3LbHQwPYy6A0pu94',"}}
[info] >      'fNgkVoyHni01zmozumbK', 'C6FceATDCJxVtji42Q3D', {"user":"    'fNgkVoyHni01zmozumbK', 'C6FceATDCJxVtji42Q3D',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'fNgkVoyHni01zmozumbK', 'C6FceATDCJxVtji42Q3D',"}}
[info] >      'M5Pahb6Vi1rdq7KZB5LF', 'oXnGAP4V78vilhIVpIQN', {"user":"    'M5Pahb6Vi1rdq7KZB5LF', 'oXnGAP4V78vilhIVpIQN',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'M5Pahb6Vi1rdq7KZB5LF', 'oXnGAP4V78vilhIVpIQN',"}}
[info] >      'NpS3CdrpHvPZClMMDeFc', 'TlS4g9PWKN1wNBtjHEoq', {"user":"    'NpS3CdrpHvPZClMMDeFc', 'TlS4g9PWKN1wNBtjHEoq',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'NpS3CdrpHvPZClMMDeFc', 'TlS4g9PWKN1wNBtjHEoq',"}}
[info] >      'rb2n32HuXJlFOk44cAkJ', 'UWkNKuXggt70Oe4bkzcx', {"user":"    'rb2n32HuXJlFOk44cAkJ', 'UWkNKuXggt70Oe4bkzcx',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'rb2n32HuXJlFOk44cAkJ', 'UWkNKuXggt70Oe4bkzcx',"}}
[info] >      'jA9Azu4oZQidKpLjPZNu', 'FwtZUF6cO4YF0pVo7CZG', {"user":"    'jA9Azu4oZQidKpLjPZNu', 'FwtZUF6cO4YF0pVo7CZG',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'jA9Azu4oZQidKpLjPZNu', 'FwtZUF6cO4YF0pVo7CZG',"}}
[info] >      '7LzTfFyZUQXm6Cl9eKL1', 'u08I4BYxrWPuwv4Byqw7', {"user":"    '7LzTfFyZUQXm6Cl9eKL1', 'u08I4BYxrWPuwv4Byqw7',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     '7LzTfFyZUQXm6Cl9eKL1', 'u08I4BYxrWPuwv4Byqw7',"}}
[info] >      'reNZlIKsdFhJiKJOhC3E' {"user":"    'reNZlIKsdFhJiKJOhC3E'","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'reNZlIKsdFhJiKJOhC3E'"}}
[info] >    ] {"user":"  ]","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   ]"}}
[info] >  } {"user":"}","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m }"}}
[info] >  🔍 REQUEST DEBUG: { {"user":"🔍 REQUEST DEBUG: {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m 🔍 REQUEST DEBUG: {"}}
[info] >    prompt: 'Normal Birth', {"user":"  prompt: 'Normal Birth',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   prompt: 'Normal Birth',"}}
[info] >    hasContext: false, {"user":"  hasContext: false,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   hasContext: false,"}}
[info] >    contextKeys: [], {"user":"  contextKeys: [],","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   contextKeys: [],"}}
[info] >    contextAction: undefined, {"user":"  contextAction: undefined,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   contextAction: undefined,"}}
[info] >    contextEditMode: undefined, {"user":"  contextEditMode: undefined,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   contextEditMode: undefined,"}}
[info] >    contextSelectedFarm: false {"user":"  contextSelectedFarm: false","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   contextSelectedFarm: false"}}
[info] >  } {"user":"}","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m }"}}
[info] >  🔍 REQUEST TYPE ANALYSIS: { {"user":"🔍 REQUEST TYPE ANALYSIS: {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m 🔍 REQUEST TYPE ANALYSIS: {"}}
[info] >    prompt: 'Normal Birth', {"user":"  prompt: 'Normal Birth',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   prompt: 'Normal Birth',"}}
[info] >    requestType: 'unknown', {"user":"  requestType: 'unknown',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   requestType: 'unknown',"}}
[info] >    hasContext: false, {"user":"  hasContext: false,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   hasContext: false,"}}
[info] >    contextAction: undefined {"user":"  contextAction: undefined","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   contextAction: undefined"}}
[info] >  } {"user":"}","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m }"}}
[info] >  📨 Context received: null {"user":"📨 Context received: null","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m 📨 Context received: null"}}
[info] >  📨 Context action: undefined {"user":"📨 Context action: undefined","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m 📨 Context action: undefined"}}
[info] >  📨 Context needsAnimalSelection: undefined {"user":"📨 Context needsAnimalSelection: undefined","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m 📨 Context needsAnimalSelection: undefined"}}
[info] >  Chat response: A normal birth, also known as a vaginal birth, is when a baby is born through the mother's vagina without the need for medical intervention such as a cesarean section. It is a natural process that can be guided by healthcare providers to ensure the safety and well-being of both the mother and the baby. If you have any specific questions about normal birth, feel free to ask! {"user":"Chat response: A normal birth, also known as a vaginal birth, is when a baby is born through the mother's vagina without the need for medical intervention such as a cesarean section. It is a natural process that can be guided by healthcare providers to ensure the safety and well-being of both the mother and the baby. If you have any specific questions about normal birth, feel free to ask!","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m Chat response: A normal birth, also known as a vaginal birth, is when a baby is born through the mother's vagina without the need for medical intervention such as a cesarean section. It is a natural process that can be guided by healthcare providers to ensure the safety and well-being of both the mother and the baby. If you have any specific questions about normal birth, feel free to ask!"}}
[debug] [2025-07-30T13:27:12.531Z] Finishing up request with event=pause {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=pause"}}
[info] i  functions: Finished "australia-southeast1-animalApp" in 2411.4335ms {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finished \"australia-southeast1-animalApp\" in 2411.4335ms"}}
[debug] [2025-07-30T13:27:12.532Z] [worker-australia-southeast1-animalApp-ba127438-ffbd-44b0-b3d0-c441c528b36f]: IDLE {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[worker-australia-southeast1-animalApp-ba127438-ffbd-44b0-b3d0-c441c528b36f]: IDLE"}}
[debug] [2025-07-30T13:27:12.532Z] Finishing up request with event=finish {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=finish"}}
[debug] [2025-07-30T13:27:12.532Z] Finishing up request with event=close {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=close"}}
[debug] [2025-07-30T13:27:12.532Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":[],"workRunningCount":0}
[debug] [2025-07-30T13:27:33.657Z] [work-queue] {"queuedWork":["/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat-2025-07-30T13:27:33.657Z"],"queueLength":1,"runningWork":[],"workRunningCount":0}
[debug] [2025-07-30T13:27:33.658Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":["/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat-2025-07-30T13:27:33.657Z"],"workRunningCount":1}
[debug] [2025-07-30T13:27:33.658Z] Accepted request OPTIONS /kissandost-9570f/australia-southeast1/animalApp/open-ai-chat --> australia-southeast1-animalApp
[debug] [2025-07-30T13:27:33.660Z] [functions] Runtime ready! Sending request! {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Runtime ready! Sending request!"}}
[debug] [2025-07-30T13:27:33.660Z] [functions] Got req.url=/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat, mapping to path=/open-ai-chat {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Got req.url=/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat, mapping to path=/open-ai-chat"}}
[debug] [2025-07-30T13:27:33.660Z] [worker-pool] submitRequest(triggerId=australia-southeast1-animalApp) {"metadata":{"emulator":{"name":"functions"},"message":"[worker-pool] submitRequest(triggerId=australia-southeast1-animalApp)"}}
[info] i  functions: Beginning execution of "australia-southeast1-animalApp" {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Beginning execution of \"australia-southeast1-animalApp\""}}
[debug] [2025-07-30T13:27:33.661Z] [worker-australia-southeast1-animalApp-ba127438-ffbd-44b0-b3d0-c441c528b36f]: BUSY {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[worker-australia-southeast1-animalApp-ba127438-ffbd-44b0-b3d0-c441c528b36f]: BUSY"}}
[debug] [2025-07-30T13:27:33.663Z] Finishing up request with event=pause {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=pause"}}
[info] i  functions: Finished "australia-southeast1-animalApp" in 2.6284ms {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finished \"australia-southeast1-animalApp\" in 2.6284ms"}}
[debug] [2025-07-30T13:27:33.694Z] [worker-australia-southeast1-animalApp-ba127438-ffbd-44b0-b3d0-c441c528b36f]: IDLE {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[worker-australia-southeast1-animalApp-ba127438-ffbd-44b0-b3d0-c441c528b36f]: IDLE"}}
[debug] [2025-07-30T13:27:33.694Z] Finishing up request with event=finish {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=finish"}}
[debug] [2025-07-30T13:27:33.694Z] Finishing up request with event=close {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=close"}}
[debug] [2025-07-30T13:27:33.694Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":[],"workRunningCount":0}
[debug] [2025-07-30T13:27:33.695Z] [work-queue] {"queuedWork":["/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat-2025-07-30T13:27:33.695Z"],"queueLength":1,"runningWork":[],"workRunningCount":0}
[debug] [2025-07-30T13:27:33.695Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":["/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat-2025-07-30T13:27:33.695Z"],"workRunningCount":1}
[debug] [2025-07-30T13:27:33.695Z] Accepted request POST /kissandost-9570f/australia-southeast1/animalApp/open-ai-chat --> australia-southeast1-animalApp
[debug] [2025-07-30T13:27:33.697Z] [functions] Runtime ready! Sending request! {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Runtime ready! Sending request!"}}
[debug] [2025-07-30T13:27:33.698Z] [functions] Got req.url=/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat, mapping to path=/open-ai-chat {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Got req.url=/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat, mapping to path=/open-ai-chat"}}
[debug] [2025-07-30T13:27:33.698Z] [worker-pool] submitRequest(triggerId=australia-southeast1-animalApp) {"metadata":{"emulator":{"name":"functions"},"message":"[worker-pool] submitRequest(triggerId=australia-southeast1-animalApp)"}}
[info] i  functions: Beginning execution of "australia-southeast1-animalApp" {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Beginning execution of \"australia-southeast1-animalApp\""}}
[debug] [2025-07-30T13:27:33.700Z] [worker-australia-southeast1-animalApp-ba127438-ffbd-44b0-b3d0-c441c528b36f]: BUSY {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[worker-australia-southeast1-animalApp-ba127438-ffbd-44b0-b3d0-c441c528b36f]: BUSY"}}
[info] >  ............................ { {"user":"............................ {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m ............................ {"}}
[info] >    id: 'obLA65ZT1JPo8Ytkrze6XAFBFE12', {"user":"  id: 'obLA65ZT1JPo8Ytkrze6XAFBFE12',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   id: 'obLA65ZT1JPo8Ytkrze6XAFBFE12',"}}
[info] >    email: '<EMAIL>', {"user":"  email: '<EMAIL>',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   email: '<EMAIL>',"}}
[info] >    role: 'owner', {"user":"  role: 'owner',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   role: 'owner',"}}
[info] >    language: 'en', {"user":"  language: 'en',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   language: 'en',"}}
[info] >    preferAudio: false, {"user":"  preferAudio: false,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   preferAudio: false,"}}
[info] >    offlineMode: false, {"user":"  offlineMode: false,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   offlineMode: false,"}}
[info] >    emailVerified: true, {"user":"  emailVerified: true,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   emailVerified: true,"}}
[info] >    createdAt: 1746969090959, {"user":"  createdAt: 1746969090959,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   createdAt: 1746969090959,"}}
[info] >    name: 'Dr. Javad Ahmed', {"user":"  name: 'Dr. Javad Ahmed',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   name: 'Dr. Javad Ahmed',"}}
[info] >    ownedFarms: [ 'ell4xZlOUxIwbSTdk4gp', 'YrwVdr1vr3KGqtXP3bqX' ], {"user":"  ownedFarms: [ 'ell4xZlOUxIwbSTdk4gp', 'YrwVdr1vr3KGqtXP3bqX' ],","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   ownedFarms: [ 'ell4xZlOUxIwbSTdk4gp', 'YrwVdr1vr3KGqtXP3bqX' ],"}}
[info] >    profilePicture: 'https://firebasestorage.googleapis.com/v0/b/kissandost-9570f.firebasestorage.app/o/profile-pictures%2FobLA65ZT1JPo8Ytkrze6XAFBFE12%2F1750768514392.jpg?alt=media&token=bb6acca1-9f21-4063-9db2-3b9994559e90', {"user":"  profilePicture: 'https://firebasestorage.googleapis.com/v0/b/kissandost-9570f.firebasestorage.app/o/profile-pictures%2FobLA65ZT1JPo8Ytkrze6XAFBFE12%2F1750768514392.jpg?alt=media&token=bb6acca1-9f21-4063-9db2-3b9994559e90',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   profilePicture: 'https://firebasestorage.googleapis.com/v0/b/kissandost-9570f.firebasestorage.app/o/profile-pictures%2FobLA65ZT1JPo8Ytkrze6XAFBFE12%2F1750768514392.jpg?alt=media&token=bb6acca1-9f21-4063-9db2-3b9994559e90',"}}
[info] >    lastLogin: Timestamp { _seconds: 1751376164, _nanoseconds: 17000000 }, {"user":"  lastLogin: Timestamp { _seconds: 1751376164, _nanoseconds: 17000000 },","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   lastLogin: Timestamp { _seconds: 1751376164, _nanoseconds: 17000000 },"}}
[info] >    updatedAt: 1753292227987, {"user":"  updatedAt: 1753292227987,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   updatedAt: 1753292227987,"}}
[info] >    assignedFarmIds: [ {"user":"  assignedFarmIds: [","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   assignedFarmIds: ["}}
[info] >      'ell4xZlOUxIwbSTdk4gp', 'YrwVdr1vr3KGqtXP3bqX', {"user":"    'ell4xZlOUxIwbSTdk4gp', 'YrwVdr1vr3KGqtXP3bqX',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'ell4xZlOUxIwbSTdk4gp', 'YrwVdr1vr3KGqtXP3bqX',"}}
[info] >      '7owH6eTfGSYqaRpgIsQE', 'icSTOLwTaC6WYgWjsgDk', {"user":"    '7owH6eTfGSYqaRpgIsQE', 'icSTOLwTaC6WYgWjsgDk',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     '7owH6eTfGSYqaRpgIsQE', 'icSTOLwTaC6WYgWjsgDk',"}}
[info] >      'BtniJTurDAn0saHHbEGF', 'wFSF3LbHQwPYy6A0pu94', {"user":"    'BtniJTurDAn0saHHbEGF', 'wFSF3LbHQwPYy6A0pu94',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'BtniJTurDAn0saHHbEGF', 'wFSF3LbHQwPYy6A0pu94',"}}
[info] >      'fNgkVoyHni01zmozumbK', 'C6FceATDCJxVtji42Q3D', {"user":"    'fNgkVoyHni01zmozumbK', 'C6FceATDCJxVtji42Q3D',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'fNgkVoyHni01zmozumbK', 'C6FceATDCJxVtji42Q3D',"}}
[info] >      'M5Pahb6Vi1rdq7KZB5LF', 'oXnGAP4V78vilhIVpIQN', {"user":"    'M5Pahb6Vi1rdq7KZB5LF', 'oXnGAP4V78vilhIVpIQN',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'M5Pahb6Vi1rdq7KZB5LF', 'oXnGAP4V78vilhIVpIQN',"}}
[info] >      'NpS3CdrpHvPZClMMDeFc', 'TlS4g9PWKN1wNBtjHEoq', {"user":"    'NpS3CdrpHvPZClMMDeFc', 'TlS4g9PWKN1wNBtjHEoq',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'NpS3CdrpHvPZClMMDeFc', 'TlS4g9PWKN1wNBtjHEoq',"}}
[info] >      'rb2n32HuXJlFOk44cAkJ', 'UWkNKuXggt70Oe4bkzcx', {"user":"    'rb2n32HuXJlFOk44cAkJ', 'UWkNKuXggt70Oe4bkzcx',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'rb2n32HuXJlFOk44cAkJ', 'UWkNKuXggt70Oe4bkzcx',"}}
[info] >      'jA9Azu4oZQidKpLjPZNu', 'FwtZUF6cO4YF0pVo7CZG', {"user":"    'jA9Azu4oZQidKpLjPZNu', 'FwtZUF6cO4YF0pVo7CZG',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'jA9Azu4oZQidKpLjPZNu', 'FwtZUF6cO4YF0pVo7CZG',"}}
[info] >      '7LzTfFyZUQXm6Cl9eKL1', 'u08I4BYxrWPuwv4Byqw7', {"user":"    '7LzTfFyZUQXm6Cl9eKL1', 'u08I4BYxrWPuwv4Byqw7',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     '7LzTfFyZUQXm6Cl9eKL1', 'u08I4BYxrWPuwv4Byqw7',"}}
[info] >      'reNZlIKsdFhJiKJOhC3E' {"user":"    'reNZlIKsdFhJiKJOhC3E'","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'reNZlIKsdFhJiKJOhC3E'"}}
[info] >    ] {"user":"  ]","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   ]"}}
[info] >  } {"user":"}","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m }"}}
[info] >  🔍 REQUEST DEBUG: { {"user":"🔍 REQUEST DEBUG: {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m 🔍 REQUEST DEBUG: {"}}
[info] >    prompt: 'Add Birth Record record', {"user":"  prompt: 'Add Birth Record record',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   prompt: 'Add Birth Record record',"}}
[info] >    hasContext: false, {"user":"  hasContext: false,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   hasContext: false,"}}
[info] >    contextKeys: [], {"user":"  contextKeys: [],","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   contextKeys: [],"}}
[info] >    contextAction: undefined, {"user":"  contextAction: undefined,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   contextAction: undefined,"}}
[info] >    contextEditMode: undefined, {"user":"  contextEditMode: undefined,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   contextEditMode: undefined,"}}
[info] >    contextSelectedFarm: false {"user":"  contextSelectedFarm: false","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   contextSelectedFarm: false"}}
[info] >  } {"user":"}","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m }"}}
[info] >  🔍 REQUEST TYPE ANALYSIS: { {"user":"🔍 REQUEST TYPE ANALYSIS: {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m 🔍 REQUEST TYPE ANALYSIS: {"}}
[info] >    prompt: 'Add Birth Record record', {"user":"  prompt: 'Add Birth Record record',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   prompt: 'Add Birth Record record',"}}
[info] >    requestType: 'add_health_record', {"user":"  requestType: 'add_health_record',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   requestType: 'add_health_record',"}}
[info] >    hasContext: false, {"user":"  hasContext: false,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   hasContext: false,"}}
[info] >    contextAction: undefined {"user":"  contextAction: undefined","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   contextAction: undefined"}}
[info] >  } {"user":"}","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m }"}}
[info] >  📨 Context received: undefined {"user":"📨 Context received: undefined","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m 📨 Context received: undefined"}}
[info] >  📨 Context action: undefined {"user":"📨 Context action: undefined","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m 📨 Context action: undefined"}}
[info] >  📨 Context needsAnimalSelection: undefined {"user":"📨 Context needsAnimalSelection: undefined","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m 📨 Context needsAnimalSelection: undefined"}}
[info] >  AI Analysis Response: ```json {"user":"AI Analysis Response: ```json","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m AI Analysis Response: ```json"}}
[info] >  { {"user":"{","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m {"}}
[info] >    "animalName": null, {"user":"  \"animalName\": null,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   \"animalName\": null,"}}
[info] >    "recordType": "birth", {"user":"  \"recordType\": \"birth\",","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   \"recordType\": \"birth\","}}
[info] >    "recordOption": null, {"user":"  \"recordOption\": null,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   \"recordOption\": null,"}}
[info] >    "practitioner": "self", {"user":"  \"practitioner\": \"self\",","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   \"practitioner\": \"self\","}}
[info] >    "date": null, {"user":"  \"date\": null,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   \"date\": null,"}}
[info] >    "notes": null, {"user":"  \"notes\": null,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   \"notes\": null,"}}
[info] >    "action": "add", {"user":"  \"action\": \"add\",","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   \"action\": \"add\","}}
[info] >    "confidence": 90 {"user":"  \"confidence\": 90","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   \"confidence\": 90"}}
[info] >  } {"user":"}","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m }"}}
[info] >  ``` {"user":"```","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m ```"}}
[info] >  AI extracted health record info: { {"user":"AI extracted health record info: {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m AI extracted health record info: {"}}
[info] >    animalName: null, {"user":"  animalName: null,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   animalName: null,"}}
[info] >    recordType: 'birth', {"user":"  recordType: 'birth',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   recordType: 'birth',"}}
[info] >    recordOption: null, {"user":"  recordOption: null,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   recordOption: null,"}}
[info] >    practitioner: 'self', {"user":"  practitioner: 'self',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   practitioner: 'self',"}}
[info] >    date: null, {"user":"  date: null,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   date: null,"}}
[info] >    notes: null, {"user":"  notes: null,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   notes: null,"}}
[info] >    action: 'add', {"user":"  action: 'add',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   action: 'add',"}}
[info] >    confidence: 90 {"user":"  confidence: 90","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   confidence: 90"}}
[info] >  } {"user":"}","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m }"}}
[info] >  Using farm from dashboard for health record: Haven_View {"user":"Using farm from dashboard for health record: Haven_View","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m Using farm from dashboard for health record: Haven_View"}}
[info] >  Available animals for health record: 5 {"user":"Available animals for health record: 5","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m Available animals for health record: 5"}}
[info] >  Need to show record options for type: birth {"user":"Need to show record options for type: birth","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m Need to show record options for type: birth"}}
[debug] [2025-07-30T13:27:36.505Z] Finishing up request with event=pause {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=pause"}}
[info] i  functions: Finished "australia-southeast1-animalApp" in 2804.9348ms {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finished \"australia-southeast1-animalApp\" in 2804.9348ms"}}
[debug] [2025-07-30T13:27:36.505Z] [worker-australia-southeast1-animalApp-ba127438-ffbd-44b0-b3d0-c441c528b36f]: IDLE {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[worker-australia-southeast1-animalApp-ba127438-ffbd-44b0-b3d0-c441c528b36f]: IDLE"}}
[debug] [2025-07-30T13:27:36.506Z] Finishing up request with event=finish {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=finish"}}
[debug] [2025-07-30T13:27:36.506Z] Finishing up request with event=close {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=close"}}
[debug] [2025-07-30T13:27:36.506Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":[],"workRunningCount":0}
[debug] [2025-07-30T13:27:41.229Z] [work-queue] {"queuedWork":["/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat-2025-07-30T13:27:41.229Z"],"queueLength":1,"runningWork":[],"workRunningCount":0}
[debug] [2025-07-30T13:27:41.230Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":["/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat-2025-07-30T13:27:41.229Z"],"workRunningCount":1}
[debug] [2025-07-30T13:27:41.230Z] Accepted request OPTIONS /kissandost-9570f/australia-southeast1/animalApp/open-ai-chat --> australia-southeast1-animalApp
[debug] [2025-07-30T13:27:41.232Z] [functions] Runtime ready! Sending request! {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Runtime ready! Sending request!"}}
[debug] [2025-07-30T13:27:41.232Z] [functions] Got req.url=/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat, mapping to path=/open-ai-chat {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Got req.url=/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat, mapping to path=/open-ai-chat"}}
[debug] [2025-07-30T13:27:41.232Z] [worker-pool] submitRequest(triggerId=australia-southeast1-animalApp) {"metadata":{"emulator":{"name":"functions"},"message":"[worker-pool] submitRequest(triggerId=australia-southeast1-animalApp)"}}
[info] i  functions: Beginning execution of "australia-southeast1-animalApp" {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Beginning execution of \"australia-southeast1-animalApp\""}}
[debug] [2025-07-30T13:27:41.233Z] [worker-australia-southeast1-animalApp-ba127438-ffbd-44b0-b3d0-c441c528b36f]: BUSY {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[worker-australia-southeast1-animalApp-ba127438-ffbd-44b0-b3d0-c441c528b36f]: BUSY"}}
[debug] [2025-07-30T13:27:41.234Z] Finishing up request with event=pause {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=pause"}}
[info] i  functions: Finished "australia-southeast1-animalApp" in 2.0144ms {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finished \"australia-southeast1-animalApp\" in 2.0144ms"}}
[debug] [2025-07-30T13:27:41.236Z] [worker-australia-southeast1-animalApp-ba127438-ffbd-44b0-b3d0-c441c528b36f]: IDLE {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[worker-australia-southeast1-animalApp-ba127438-ffbd-44b0-b3d0-c441c528b36f]: IDLE"}}
[debug] [2025-07-30T13:27:41.236Z] Finishing up request with event=finish {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=finish"}}
[debug] [2025-07-30T13:27:41.236Z] Finishing up request with event=close {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=close"}}
[debug] [2025-07-30T13:27:41.236Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":[],"workRunningCount":0}
[debug] [2025-07-30T13:27:41.237Z] [work-queue] {"queuedWork":["/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat-2025-07-30T13:27:41.237Z"],"queueLength":1,"runningWork":[],"workRunningCount":0}
[debug] [2025-07-30T13:27:41.237Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":["/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat-2025-07-30T13:27:41.237Z"],"workRunningCount":1}
[debug] [2025-07-30T13:27:41.237Z] Accepted request POST /kissandost-9570f/australia-southeast1/animalApp/open-ai-chat --> australia-southeast1-animalApp
[debug] [2025-07-30T13:27:41.239Z] [functions] Runtime ready! Sending request! {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Runtime ready! Sending request!"}}
[debug] [2025-07-30T13:27:41.239Z] [functions] Got req.url=/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat, mapping to path=/open-ai-chat {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Got req.url=/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat, mapping to path=/open-ai-chat"}}
[debug] [2025-07-30T13:27:41.239Z] [worker-pool] submitRequest(triggerId=australia-southeast1-animalApp) {"metadata":{"emulator":{"name":"functions"},"message":"[worker-pool] submitRequest(triggerId=australia-southeast1-animalApp)"}}
[info] i  functions: Beginning execution of "australia-southeast1-animalApp" {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Beginning execution of \"australia-southeast1-animalApp\""}}
[debug] [2025-07-30T13:27:41.240Z] [worker-australia-southeast1-animalApp-ba127438-ffbd-44b0-b3d0-c441c528b36f]: BUSY {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[worker-australia-southeast1-animalApp-ba127438-ffbd-44b0-b3d0-c441c528b36f]: BUSY"}}
[info] >  ............................ { {"user":"............................ {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m ............................ {"}}
[info] >    id: 'obLA65ZT1JPo8Ytkrze6XAFBFE12', {"user":"  id: 'obLA65ZT1JPo8Ytkrze6XAFBFE12',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   id: 'obLA65ZT1JPo8Ytkrze6XAFBFE12',"}}
[info] >    email: '<EMAIL>', {"user":"  email: '<EMAIL>',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   email: '<EMAIL>',"}}
[info] >    role: 'owner', {"user":"  role: 'owner',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   role: 'owner',"}}
[info] >    language: 'en', {"user":"  language: 'en',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   language: 'en',"}}
[info] >    preferAudio: false, {"user":"  preferAudio: false,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   preferAudio: false,"}}
[info] >    offlineMode: false, {"user":"  offlineMode: false,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   offlineMode: false,"}}
[info] >    emailVerified: true, {"user":"  emailVerified: true,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   emailVerified: true,"}}
[info] >    createdAt: 1746969090959, {"user":"  createdAt: 1746969090959,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   createdAt: 1746969090959,"}}
[info] >    name: 'Dr. Javad Ahmed', {"user":"  name: 'Dr. Javad Ahmed',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   name: 'Dr. Javad Ahmed',"}}
[info] >    ownedFarms: [ 'ell4xZlOUxIwbSTdk4gp', 'YrwVdr1vr3KGqtXP3bqX' ], {"user":"  ownedFarms: [ 'ell4xZlOUxIwbSTdk4gp', 'YrwVdr1vr3KGqtXP3bqX' ],","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   ownedFarms: [ 'ell4xZlOUxIwbSTdk4gp', 'YrwVdr1vr3KGqtXP3bqX' ],"}}
[info] >    profilePicture: 'https://firebasestorage.googleapis.com/v0/b/kissandost-9570f.firebasestorage.app/o/profile-pictures%2FobLA65ZT1JPo8Ytkrze6XAFBFE12%2F1750768514392.jpg?alt=media&token=bb6acca1-9f21-4063-9db2-3b9994559e90', {"user":"  profilePicture: 'https://firebasestorage.googleapis.com/v0/b/kissandost-9570f.firebasestorage.app/o/profile-pictures%2FobLA65ZT1JPo8Ytkrze6XAFBFE12%2F1750768514392.jpg?alt=media&token=bb6acca1-9f21-4063-9db2-3b9994559e90',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   profilePicture: 'https://firebasestorage.googleapis.com/v0/b/kissandost-9570f.firebasestorage.app/o/profile-pictures%2FobLA65ZT1JPo8Ytkrze6XAFBFE12%2F1750768514392.jpg?alt=media&token=bb6acca1-9f21-4063-9db2-3b9994559e90',"}}
[info] >    lastLogin: Timestamp { _seconds: 1751376164, _nanoseconds: 17000000 }, {"user":"  lastLogin: Timestamp { _seconds: 1751376164, _nanoseconds: 17000000 },","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   lastLogin: Timestamp { _seconds: 1751376164, _nanoseconds: 17000000 },"}}
[info] >    updatedAt: 1753292227987, {"user":"  updatedAt: 1753292227987,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   updatedAt: 1753292227987,"}}
[info] >    assignedFarmIds: [ {"user":"  assignedFarmIds: [","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   assignedFarmIds: ["}}
[info] >      'ell4xZlOUxIwbSTdk4gp', 'YrwVdr1vr3KGqtXP3bqX', {"user":"    'ell4xZlOUxIwbSTdk4gp', 'YrwVdr1vr3KGqtXP3bqX',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'ell4xZlOUxIwbSTdk4gp', 'YrwVdr1vr3KGqtXP3bqX',"}}
[info] >      '7owH6eTfGSYqaRpgIsQE', 'icSTOLwTaC6WYgWjsgDk', {"user":"    '7owH6eTfGSYqaRpgIsQE', 'icSTOLwTaC6WYgWjsgDk',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     '7owH6eTfGSYqaRpgIsQE', 'icSTOLwTaC6WYgWjsgDk',"}}
[info] >      'BtniJTurDAn0saHHbEGF', 'wFSF3LbHQwPYy6A0pu94', {"user":"    'BtniJTurDAn0saHHbEGF', 'wFSF3LbHQwPYy6A0pu94',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'BtniJTurDAn0saHHbEGF', 'wFSF3LbHQwPYy6A0pu94',"}}
[info] >      'fNgkVoyHni01zmozumbK', 'C6FceATDCJxVtji42Q3D', {"user":"    'fNgkVoyHni01zmozumbK', 'C6FceATDCJxVtji42Q3D',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'fNgkVoyHni01zmozumbK', 'C6FceATDCJxVtji42Q3D',"}}
[info] >      'M5Pahb6Vi1rdq7KZB5LF', 'oXnGAP4V78vilhIVpIQN', {"user":"    'M5Pahb6Vi1rdq7KZB5LF', 'oXnGAP4V78vilhIVpIQN',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'M5Pahb6Vi1rdq7KZB5LF', 'oXnGAP4V78vilhIVpIQN',"}}
[info] >      'NpS3CdrpHvPZClMMDeFc', 'TlS4g9PWKN1wNBtjHEoq', {"user":"    'NpS3CdrpHvPZClMMDeFc', 'TlS4g9PWKN1wNBtjHEoq',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'NpS3CdrpHvPZClMMDeFc', 'TlS4g9PWKN1wNBtjHEoq',"}}
[info] >      'rb2n32HuXJlFOk44cAkJ', 'UWkNKuXggt70Oe4bkzcx', {"user":"    'rb2n32HuXJlFOk44cAkJ', 'UWkNKuXggt70Oe4bkzcx',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'rb2n32HuXJlFOk44cAkJ', 'UWkNKuXggt70Oe4bkzcx',"}}
[info] >      'jA9Azu4oZQidKpLjPZNu', 'FwtZUF6cO4YF0pVo7CZG', {"user":"    'jA9Azu4oZQidKpLjPZNu', 'FwtZUF6cO4YF0pVo7CZG',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'jA9Azu4oZQidKpLjPZNu', 'FwtZUF6cO4YF0pVo7CZG',"}}
[info] >      '7LzTfFyZUQXm6Cl9eKL1', 'u08I4BYxrWPuwv4Byqw7', {"user":"    '7LzTfFyZUQXm6Cl9eKL1', 'u08I4BYxrWPuwv4Byqw7',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     '7LzTfFyZUQXm6Cl9eKL1', 'u08I4BYxrWPuwv4Byqw7',"}}
[info] >      'reNZlIKsdFhJiKJOhC3E' {"user":"    'reNZlIKsdFhJiKJOhC3E'","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'reNZlIKsdFhJiKJOhC3E'"}}
[info] >    ] {"user":"  ]","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   ]"}}
[info] >  } {"user":"}","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m }"}}
[info] >  🔍 REQUEST DEBUG: { {"user":"🔍 REQUEST DEBUG: {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m 🔍 REQUEST DEBUG: {"}}
[info] >    prompt: 'Normal Birth', {"user":"  prompt: 'Normal Birth',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   prompt: 'Normal Birth',"}}
[info] >    hasContext: false, {"user":"  hasContext: false,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   hasContext: false,"}}
[info] >    contextKeys: [], {"user":"  contextKeys: [],","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   contextKeys: [],"}}
[info] >    contextAction: undefined, {"user":"  contextAction: undefined,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   contextAction: undefined,"}}
[info] >    contextEditMode: undefined, {"user":"  contextEditMode: undefined,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   contextEditMode: undefined,"}}
[info] >    contextSelectedFarm: false {"user":"  contextSelectedFarm: false","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   contextSelectedFarm: false"}}
[info] >  } {"user":"}","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m }"}}
[info] >  🔍 REQUEST TYPE ANALYSIS: { {"user":"🔍 REQUEST TYPE ANALYSIS: {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m 🔍 REQUEST TYPE ANALYSIS: {"}}
[info] >    prompt: 'Normal Birth', {"user":"  prompt: 'Normal Birth',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   prompt: 'Normal Birth',"}}
[info] >    requestType: 'unknown', {"user":"  requestType: 'unknown',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   requestType: 'unknown',"}}
[info] >    hasContext: false, {"user":"  hasContext: false,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   hasContext: false,"}}
[info] >    contextAction: undefined {"user":"  contextAction: undefined","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   contextAction: undefined"}}
[info] >  } {"user":"}","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m }"}}
[info] >  📨 Context received: null {"user":"📨 Context received: null","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m 📨 Context received: null"}}
[info] >  📨 Context action: undefined {"user":"📨 Context action: undefined","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m 📨 Context action: undefined"}}
[info] >  📨 Context needsAnimalSelection: undefined {"user":"📨 Context needsAnimalSelection: undefined","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m 📨 Context needsAnimalSelection: undefined"}}
[info] >  Chat response: Normal birth, also known as vaginal birth, is when a baby is born through the mother's birth canal without the need for medical intervention such as a C-section. It is a natural process that happens when both the mother and baby are healthy and the labor progresses smoothly. It is important for expectant mothers to discuss their birth preferences with their healthcare provider to create a birth plan that aligns with their needs and preferences. {"user":"Chat response: Normal birth, also known as vaginal birth, is when a baby is born through the mother's birth canal without the need for medical intervention such as a C-section. It is a natural process that happens when both the mother and baby are healthy and the labor progresses smoothly. It is important for expectant mothers to discuss their birth preferences with their healthcare provider to create a birth plan that aligns with their needs and preferences.","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m Chat response: Normal birth, also known as vaginal birth, is when a baby is born through the mother's birth canal without the need for medical intervention such as a C-section. It is a natural process that happens when both the mother and baby are healthy and the labor progresses smoothly. It is important for expectant mothers to discuss their birth preferences with their healthcare provider to create a birth plan that aligns with their needs and preferences."}}
[debug] [2025-07-30T13:27:43.088Z] Finishing up request with event=pause {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=pause"}}
[info] i  functions: Finished "australia-southeast1-animalApp" in 1847.7865000000002ms {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finished \"australia-southeast1-animalApp\" in 1847.7865000000002ms"}}
[debug] [2025-07-30T13:27:43.088Z] [worker-australia-southeast1-animalApp-ba127438-ffbd-44b0-b3d0-c441c528b36f]: IDLE {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[worker-australia-southeast1-animalApp-ba127438-ffbd-44b0-b3d0-c441c528b36f]: IDLE"}}
[debug] [2025-07-30T13:27:43.088Z] Finishing up request with event=finish {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=finish"}}
[debug] [2025-07-30T13:27:43.089Z] Finishing up request with event=close {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=close"}}
[debug] [2025-07-30T13:27:43.089Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":[],"workRunningCount":0}
[debug] [2025-07-30T13:31:01.550Z] [work-queue] {"queuedWork":["/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat-2025-07-30T13:31:01.550Z"],"queueLength":1,"runningWork":[],"workRunningCount":0}
[debug] [2025-07-30T13:31:01.551Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":["/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat-2025-07-30T13:31:01.550Z"],"workRunningCount":1}
[debug] [2025-07-30T13:31:01.551Z] Accepted request OPTIONS /kissandost-9570f/australia-southeast1/animalApp/open-ai-chat --> australia-southeast1-animalApp
[debug] [2025-07-30T13:31:01.553Z] [functions] Runtime ready! Sending request! {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Runtime ready! Sending request!"}}
[debug] [2025-07-30T13:31:01.553Z] [functions] Got req.url=/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat, mapping to path=/open-ai-chat {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Got req.url=/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat, mapping to path=/open-ai-chat"}}
[debug] [2025-07-30T13:31:01.553Z] [worker-pool] submitRequest(triggerId=australia-southeast1-animalApp) {"metadata":{"emulator":{"name":"functions"},"message":"[worker-pool] submitRequest(triggerId=australia-southeast1-animalApp)"}}
[info] i  functions: Beginning execution of "australia-southeast1-animalApp" {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Beginning execution of \"australia-southeast1-animalApp\""}}
[debug] [2025-07-30T13:31:01.554Z] [worker-australia-southeast1-animalApp-ba127438-ffbd-44b0-b3d0-c441c528b36f]: BUSY {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[worker-australia-southeast1-animalApp-ba127438-ffbd-44b0-b3d0-c441c528b36f]: BUSY"}}
[debug] [2025-07-30T13:31:01.556Z] Finishing up request with event=pause {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=pause"}}
[info] i  functions: Finished "australia-southeast1-animalApp" in 2.0975ms {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finished \"australia-southeast1-animalApp\" in 2.0975ms"}}
[debug] [2025-07-30T13:31:01.557Z] [worker-australia-southeast1-animalApp-ba127438-ffbd-44b0-b3d0-c441c528b36f]: IDLE {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[worker-australia-southeast1-animalApp-ba127438-ffbd-44b0-b3d0-c441c528b36f]: IDLE"}}
[debug] [2025-07-30T13:31:01.558Z] Finishing up request with event=finish {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=finish"}}
[debug] [2025-07-30T13:31:01.558Z] Finishing up request with event=close {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=close"}}
[debug] [2025-07-30T13:31:01.558Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":[],"workRunningCount":0}
[debug] [2025-07-30T13:31:01.559Z] [work-queue] {"queuedWork":["/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat-2025-07-30T13:31:01.559Z"],"queueLength":1,"runningWork":[],"workRunningCount":0}
[debug] [2025-07-30T13:31:01.559Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":["/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat-2025-07-30T13:31:01.559Z"],"workRunningCount":1}
[debug] [2025-07-30T13:31:01.559Z] Accepted request POST /kissandost-9570f/australia-southeast1/animalApp/open-ai-chat --> australia-southeast1-animalApp
[debug] [2025-07-30T13:31:01.562Z] [functions] Runtime ready! Sending request! {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Runtime ready! Sending request!"}}
[debug] [2025-07-30T13:31:01.562Z] [functions] Got req.url=/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat, mapping to path=/open-ai-chat {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Got req.url=/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat, mapping to path=/open-ai-chat"}}
[debug] [2025-07-30T13:31:01.562Z] [worker-pool] submitRequest(triggerId=australia-southeast1-animalApp) {"metadata":{"emulator":{"name":"functions"},"message":"[worker-pool] submitRequest(triggerId=australia-southeast1-animalApp)"}}
[info] i  functions: Beginning execution of "australia-southeast1-animalApp" {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Beginning execution of \"australia-southeast1-animalApp\""}}
[debug] [2025-07-30T13:31:01.568Z] [worker-australia-southeast1-animalApp-ba127438-ffbd-44b0-b3d0-c441c528b36f]: BUSY {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[worker-australia-southeast1-animalApp-ba127438-ffbd-44b0-b3d0-c441c528b36f]: BUSY"}}
[info] >  ............................ { {"user":"............................ {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m ............................ {"}}
[info] >    id: 'obLA65ZT1JPo8Ytkrze6XAFBFE12', {"user":"  id: 'obLA65ZT1JPo8Ytkrze6XAFBFE12',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   id: 'obLA65ZT1JPo8Ytkrze6XAFBFE12',"}}
[info] >    email: '<EMAIL>', {"user":"  email: '<EMAIL>',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   email: '<EMAIL>',"}}
[info] >    role: 'owner', {"user":"  role: 'owner',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   role: 'owner',"}}
[info] >    language: 'en', {"user":"  language: 'en',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   language: 'en',"}}
[info] >    preferAudio: false, {"user":"  preferAudio: false,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   preferAudio: false,"}}
[info] >    offlineMode: false, {"user":"  offlineMode: false,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   offlineMode: false,"}}
[info] >    emailVerified: true, {"user":"  emailVerified: true,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   emailVerified: true,"}}
[info] >    createdAt: 1746969090959, {"user":"  createdAt: 1746969090959,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   createdAt: 1746969090959,"}}
[info] >    name: 'Dr. Javad Ahmed', {"user":"  name: 'Dr. Javad Ahmed',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   name: 'Dr. Javad Ahmed',"}}
[info] >    ownedFarms: [ 'ell4xZlOUxIwbSTdk4gp', 'YrwVdr1vr3KGqtXP3bqX' ], {"user":"  ownedFarms: [ 'ell4xZlOUxIwbSTdk4gp', 'YrwVdr1vr3KGqtXP3bqX' ],","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   ownedFarms: [ 'ell4xZlOUxIwbSTdk4gp', 'YrwVdr1vr3KGqtXP3bqX' ],"}}
[info] >    profilePicture: 'https://firebasestorage.googleapis.com/v0/b/kissandost-9570f.firebasestorage.app/o/profile-pictures%2FobLA65ZT1JPo8Ytkrze6XAFBFE12%2F1750768514392.jpg?alt=media&token=bb6acca1-9f21-4063-9db2-3b9994559e90', {"user":"  profilePicture: 'https://firebasestorage.googleapis.com/v0/b/kissandost-9570f.firebasestorage.app/o/profile-pictures%2FobLA65ZT1JPo8Ytkrze6XAFBFE12%2F1750768514392.jpg?alt=media&token=bb6acca1-9f21-4063-9db2-3b9994559e90',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   profilePicture: 'https://firebasestorage.googleapis.com/v0/b/kissandost-9570f.firebasestorage.app/o/profile-pictures%2FobLA65ZT1JPo8Ytkrze6XAFBFE12%2F1750768514392.jpg?alt=media&token=bb6acca1-9f21-4063-9db2-3b9994559e90',"}}
[info] >    lastLogin: Timestamp { _seconds: 1751376164, _nanoseconds: 17000000 }, {"user":"  lastLogin: Timestamp { _seconds: 1751376164, _nanoseconds: 17000000 },","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   lastLogin: Timestamp { _seconds: 1751376164, _nanoseconds: 17000000 },"}}
[info] >    updatedAt: 1753292227987, {"user":"  updatedAt: 1753292227987,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   updatedAt: 1753292227987,"}}
[info] >    assignedFarmIds: [ {"user":"  assignedFarmIds: [","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   assignedFarmIds: ["}}
[info] >      'ell4xZlOUxIwbSTdk4gp', 'YrwVdr1vr3KGqtXP3bqX', {"user":"    'ell4xZlOUxIwbSTdk4gp', 'YrwVdr1vr3KGqtXP3bqX',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'ell4xZlOUxIwbSTdk4gp', 'YrwVdr1vr3KGqtXP3bqX',"}}
[info] >      '7owH6eTfGSYqaRpgIsQE', 'icSTOLwTaC6WYgWjsgDk', {"user":"    '7owH6eTfGSYqaRpgIsQE', 'icSTOLwTaC6WYgWjsgDk',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     '7owH6eTfGSYqaRpgIsQE', 'icSTOLwTaC6WYgWjsgDk',"}}
[info] >      'BtniJTurDAn0saHHbEGF', 'wFSF3LbHQwPYy6A0pu94', {"user":"    'BtniJTurDAn0saHHbEGF', 'wFSF3LbHQwPYy6A0pu94',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'BtniJTurDAn0saHHbEGF', 'wFSF3LbHQwPYy6A0pu94',"}}
[info] >      'fNgkVoyHni01zmozumbK', 'C6FceATDCJxVtji42Q3D', {"user":"    'fNgkVoyHni01zmozumbK', 'C6FceATDCJxVtji42Q3D',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'fNgkVoyHni01zmozumbK', 'C6FceATDCJxVtji42Q3D',"}}
[info] >      'M5Pahb6Vi1rdq7KZB5LF', 'oXnGAP4V78vilhIVpIQN', {"user":"    'M5Pahb6Vi1rdq7KZB5LF', 'oXnGAP4V78vilhIVpIQN',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'M5Pahb6Vi1rdq7KZB5LF', 'oXnGAP4V78vilhIVpIQN',"}}
[info] >      'NpS3CdrpHvPZClMMDeFc', 'TlS4g9PWKN1wNBtjHEoq', {"user":"    'NpS3CdrpHvPZClMMDeFc', 'TlS4g9PWKN1wNBtjHEoq',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'NpS3CdrpHvPZClMMDeFc', 'TlS4g9PWKN1wNBtjHEoq',"}}
[info] >      'rb2n32HuXJlFOk44cAkJ', 'UWkNKuXggt70Oe4bkzcx', {"user":"    'rb2n32HuXJlFOk44cAkJ', 'UWkNKuXggt70Oe4bkzcx',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'rb2n32HuXJlFOk44cAkJ', 'UWkNKuXggt70Oe4bkzcx',"}}
[info] >      'jA9Azu4oZQidKpLjPZNu', 'FwtZUF6cO4YF0pVo7CZG', {"user":"    'jA9Azu4oZQidKpLjPZNu', 'FwtZUF6cO4YF0pVo7CZG',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'jA9Azu4oZQidKpLjPZNu', 'FwtZUF6cO4YF0pVo7CZG',"}}
[info] >      '7LzTfFyZUQXm6Cl9eKL1', 'u08I4BYxrWPuwv4Byqw7', {"user":"    '7LzTfFyZUQXm6Cl9eKL1', 'u08I4BYxrWPuwv4Byqw7',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     '7LzTfFyZUQXm6Cl9eKL1', 'u08I4BYxrWPuwv4Byqw7',"}}
[info] >      'reNZlIKsdFhJiKJOhC3E' {"user":"    'reNZlIKsdFhJiKJOhC3E'","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'reNZlIKsdFhJiKJOhC3E'"}}
[info] >    ] {"user":"  ]","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   ]"}}
[info] >  } {"user":"}","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m }"}}
[info] >  🔍 REQUEST DEBUG: { {"user":"🔍 REQUEST DEBUG: {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m 🔍 REQUEST DEBUG: {"}}
[info] >    prompt: 'Add Birth Record record', {"user":"  prompt: 'Add Birth Record record',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   prompt: 'Add Birth Record record',"}}
[info] >    hasContext: false, {"user":"  hasContext: false,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   hasContext: false,"}}
[info] >    contextKeys: [], {"user":"  contextKeys: [],","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   contextKeys: [],"}}
[info] >    contextAction: undefined, {"user":"  contextAction: undefined,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   contextAction: undefined,"}}
[info] >    contextEditMode: undefined, {"user":"  contextEditMode: undefined,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   contextEditMode: undefined,"}}
[info] >    contextSelectedFarm: false {"user":"  contextSelectedFarm: false","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   contextSelectedFarm: false"}}
[info] >  } {"user":"}","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m }"}}
[info] >  🔍 REQUEST TYPE ANALYSIS: { {"user":"🔍 REQUEST TYPE ANALYSIS: {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m 🔍 REQUEST TYPE ANALYSIS: {"}}
[info] >    prompt: 'Add Birth Record record', {"user":"  prompt: 'Add Birth Record record',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   prompt: 'Add Birth Record record',"}}
[info] >    requestType: 'add_health_record', {"user":"  requestType: 'add_health_record',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   requestType: 'add_health_record',"}}
[info] >    hasContext: false, {"user":"  hasContext: false,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   hasContext: false,"}}
[info] >    contextAction: undefined {"user":"  contextAction: undefined","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   contextAction: undefined"}}
[info] >  } {"user":"}","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m }"}}
[info] >  📨 Context received: undefined {"user":"📨 Context received: undefined","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m 📨 Context received: undefined"}}
[info] >  📨 Context action: undefined {"user":"📨 Context action: undefined","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m 📨 Context action: undefined"}}
[info] >  📨 Context needsAnimalSelection: undefined {"user":"📨 Context needsAnimalSelection: undefined","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m 📨 Context needsAnimalSelection: undefined"}}
[info] >  AI Analysis Response: ```json {"user":"AI Analysis Response: ```json","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m AI Analysis Response: ```json"}}
[info] >  { {"user":"{","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m {"}}
[info] >    "animalName": null, {"user":"  \"animalName\": null,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   \"animalName\": null,"}}
[info] >    "recordType": "birth", {"user":"  \"recordType\": \"birth\",","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   \"recordType\": \"birth\","}}
[info] >    "recordOption": null, {"user":"  \"recordOption\": null,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   \"recordOption\": null,"}}
[info] >    "practitioner": "self", {"user":"  \"practitioner\": \"self\",","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   \"practitioner\": \"self\","}}
[info] >    "date": null, {"user":"  \"date\": null,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   \"date\": null,"}}
[info] >    "notes": null, {"user":"  \"notes\": null,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   \"notes\": null,"}}
[info] >    "action": "add", {"user":"  \"action\": \"add\",","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   \"action\": \"add\","}}
[info] >    "confidence": 90 {"user":"  \"confidence\": 90","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   \"confidence\": 90"}}
[info] >  } {"user":"}","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m }"}}
[info] >  ``` {"user":"```","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m ```"}}
[info] >  AI extracted health record info: { {"user":"AI extracted health record info: {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m AI extracted health record info: {"}}
[info] >    animalName: null, {"user":"  animalName: null,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   animalName: null,"}}
[info] >    recordType: 'birth', {"user":"  recordType: 'birth',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   recordType: 'birth',"}}
[info] >    recordOption: null, {"user":"  recordOption: null,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   recordOption: null,"}}
[info] >    practitioner: 'self', {"user":"  practitioner: 'self',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   practitioner: 'self',"}}
[info] >    date: null, {"user":"  date: null,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   date: null,"}}
[info] >    notes: null, {"user":"  notes: null,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   notes: null,"}}
[info] >    action: 'add', {"user":"  action: 'add',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   action: 'add',"}}
[info] >    confidence: 90 {"user":"  confidence: 90","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   confidence: 90"}}
[info] >  } {"user":"}","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m }"}}
[info] >  Using farm from dashboard for health record: Haven_View {"user":"Using farm from dashboard for health record: Haven_View","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m Using farm from dashboard for health record: Haven_View"}}
[info] >  Available animals for health record: 5 {"user":"Available animals for health record: 5","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m Available animals for health record: 5"}}
[info] >  Need to show record options for type: birth {"user":"Need to show record options for type: birth","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m Need to show record options for type: birth"}}
[debug] [2025-07-30T13:31:04.518Z] Finishing up request with event=pause {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=pause"}}
[info] i  functions: Finished "australia-southeast1-animalApp" in 2949.9210000000003ms {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finished \"australia-southeast1-animalApp\" in 2949.9210000000003ms"}}
[debug] [2025-07-30T13:31:04.519Z] [worker-australia-southeast1-animalApp-ba127438-ffbd-44b0-b3d0-c441c528b36f]: IDLE {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[worker-australia-southeast1-animalApp-ba127438-ffbd-44b0-b3d0-c441c528b36f]: IDLE"}}
[debug] [2025-07-30T13:31:04.519Z] Finishing up request with event=finish {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=finish"}}
[debug] [2025-07-30T13:31:04.519Z] Finishing up request with event=close {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=close"}}
[debug] [2025-07-30T13:31:04.519Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":[],"workRunningCount":0}
[debug] [2025-07-30T13:31:11.425Z] [work-queue] {"queuedWork":["/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat-2025-07-30T13:31:11.425Z"],"queueLength":1,"runningWork":[],"workRunningCount":0}
[debug] [2025-07-30T13:31:11.426Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":["/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat-2025-07-30T13:31:11.425Z"],"workRunningCount":1}
[debug] [2025-07-30T13:31:11.426Z] Accepted request OPTIONS /kissandost-9570f/australia-southeast1/animalApp/open-ai-chat --> australia-southeast1-animalApp
[debug] [2025-07-30T13:31:11.428Z] [functions] Runtime ready! Sending request! {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Runtime ready! Sending request!"}}
[debug] [2025-07-30T13:31:11.428Z] [functions] Got req.url=/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat, mapping to path=/open-ai-chat {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Got req.url=/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat, mapping to path=/open-ai-chat"}}
[debug] [2025-07-30T13:31:11.428Z] [worker-pool] submitRequest(triggerId=australia-southeast1-animalApp) {"metadata":{"emulator":{"name":"functions"},"message":"[worker-pool] submitRequest(triggerId=australia-southeast1-animalApp)"}}
[info] i  functions: Beginning execution of "australia-southeast1-animalApp" {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Beginning execution of \"australia-southeast1-animalApp\""}}
[debug] [2025-07-30T13:31:11.429Z] [worker-australia-southeast1-animalApp-ba127438-ffbd-44b0-b3d0-c441c528b36f]: BUSY {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[worker-australia-southeast1-animalApp-ba127438-ffbd-44b0-b3d0-c441c528b36f]: BUSY"}}
[debug] [2025-07-30T13:31:11.432Z] Finishing up request with event=pause {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=pause"}}
[info] i  functions: Finished "australia-southeast1-animalApp" in 2.4432ms {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finished \"australia-southeast1-animalApp\" in 2.4432ms"}}
[debug] [2025-07-30T13:31:11.439Z] [worker-australia-southeast1-animalApp-ba127438-ffbd-44b0-b3d0-c441c528b36f]: IDLE {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[worker-australia-southeast1-animalApp-ba127438-ffbd-44b0-b3d0-c441c528b36f]: IDLE"}}
[debug] [2025-07-30T13:31:11.439Z] Finishing up request with event=finish {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=finish"}}
[debug] [2025-07-30T13:31:11.439Z] Finishing up request with event=close {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=close"}}
[debug] [2025-07-30T13:31:11.439Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":[],"workRunningCount":0}
[debug] [2025-07-30T13:31:11.439Z] [work-queue] {"queuedWork":["/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat-2025-07-30T13:31:11.439Z"],"queueLength":1,"runningWork":[],"workRunningCount":0}
[debug] [2025-07-30T13:31:11.440Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":["/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat-2025-07-30T13:31:11.439Z"],"workRunningCount":1}
[debug] [2025-07-30T13:31:11.440Z] Accepted request POST /kissandost-9570f/australia-southeast1/animalApp/open-ai-chat --> australia-southeast1-animalApp
[debug] [2025-07-30T13:31:11.442Z] [functions] Runtime ready! Sending request! {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Runtime ready! Sending request!"}}
[debug] [2025-07-30T13:31:11.442Z] [functions] Got req.url=/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat, mapping to path=/open-ai-chat {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Got req.url=/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat, mapping to path=/open-ai-chat"}}
[debug] [2025-07-30T13:31:11.442Z] [worker-pool] submitRequest(triggerId=australia-southeast1-animalApp) {"metadata":{"emulator":{"name":"functions"},"message":"[worker-pool] submitRequest(triggerId=australia-southeast1-animalApp)"}}
[info] i  functions: Beginning execution of "australia-southeast1-animalApp" {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Beginning execution of \"australia-southeast1-animalApp\""}}
[debug] [2025-07-30T13:31:11.443Z] [worker-australia-southeast1-animalApp-ba127438-ffbd-44b0-b3d0-c441c528b36f]: BUSY {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[worker-australia-southeast1-animalApp-ba127438-ffbd-44b0-b3d0-c441c528b36f]: BUSY"}}
[info] >  ............................ { {"user":"............................ {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m ............................ {"}}
[info] >    id: 'obLA65ZT1JPo8Ytkrze6XAFBFE12', {"user":"  id: 'obLA65ZT1JPo8Ytkrze6XAFBFE12',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   id: 'obLA65ZT1JPo8Ytkrze6XAFBFE12',"}}
[info] >    email: '<EMAIL>', {"user":"  email: '<EMAIL>',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   email: '<EMAIL>',"}}
[info] >    role: 'owner', {"user":"  role: 'owner',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   role: 'owner',"}}
[info] >    language: 'en', {"user":"  language: 'en',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   language: 'en',"}}
[info] >    preferAudio: false, {"user":"  preferAudio: false,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   preferAudio: false,"}}
[info] >    offlineMode: false, {"user":"  offlineMode: false,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   offlineMode: false,"}}
[info] >    emailVerified: true, {"user":"  emailVerified: true,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   emailVerified: true,"}}
[info] >    createdAt: 1746969090959, {"user":"  createdAt: 1746969090959,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   createdAt: 1746969090959,"}}
[info] >    name: 'Dr. Javad Ahmed', {"user":"  name: 'Dr. Javad Ahmed',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   name: 'Dr. Javad Ahmed',"}}
[info] >    ownedFarms: [ 'ell4xZlOUxIwbSTdk4gp', 'YrwVdr1vr3KGqtXP3bqX' ], {"user":"  ownedFarms: [ 'ell4xZlOUxIwbSTdk4gp', 'YrwVdr1vr3KGqtXP3bqX' ],","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   ownedFarms: [ 'ell4xZlOUxIwbSTdk4gp', 'YrwVdr1vr3KGqtXP3bqX' ],"}}
[info] >    profilePicture: 'https://firebasestorage.googleapis.com/v0/b/kissandost-9570f.firebasestorage.app/o/profile-pictures%2FobLA65ZT1JPo8Ytkrze6XAFBFE12%2F1750768514392.jpg?alt=media&token=bb6acca1-9f21-4063-9db2-3b9994559e90', {"user":"  profilePicture: 'https://firebasestorage.googleapis.com/v0/b/kissandost-9570f.firebasestorage.app/o/profile-pictures%2FobLA65ZT1JPo8Ytkrze6XAFBFE12%2F1750768514392.jpg?alt=media&token=bb6acca1-9f21-4063-9db2-3b9994559e90',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   profilePicture: 'https://firebasestorage.googleapis.com/v0/b/kissandost-9570f.firebasestorage.app/o/profile-pictures%2FobLA65ZT1JPo8Ytkrze6XAFBFE12%2F1750768514392.jpg?alt=media&token=bb6acca1-9f21-4063-9db2-3b9994559e90',"}}
[info] >    lastLogin: Timestamp { _seconds: 1751376164, _nanoseconds: 17000000 }, {"user":"  lastLogin: Timestamp { _seconds: 1751376164, _nanoseconds: 17000000 },","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   lastLogin: Timestamp { _seconds: 1751376164, _nanoseconds: 17000000 },"}}
[info] >    updatedAt: 1753292227987, {"user":"  updatedAt: 1753292227987,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   updatedAt: 1753292227987,"}}
[info] >    assignedFarmIds: [ {"user":"  assignedFarmIds: [","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   assignedFarmIds: ["}}
[info] >      'ell4xZlOUxIwbSTdk4gp', 'YrwVdr1vr3KGqtXP3bqX', {"user":"    'ell4xZlOUxIwbSTdk4gp', 'YrwVdr1vr3KGqtXP3bqX',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'ell4xZlOUxIwbSTdk4gp', 'YrwVdr1vr3KGqtXP3bqX',"}}
[info] >      '7owH6eTfGSYqaRpgIsQE', 'icSTOLwTaC6WYgWjsgDk', {"user":"    '7owH6eTfGSYqaRpgIsQE', 'icSTOLwTaC6WYgWjsgDk',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     '7owH6eTfGSYqaRpgIsQE', 'icSTOLwTaC6WYgWjsgDk',"}}
[info] >      'BtniJTurDAn0saHHbEGF', 'wFSF3LbHQwPYy6A0pu94', {"user":"    'BtniJTurDAn0saHHbEGF', 'wFSF3LbHQwPYy6A0pu94',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'BtniJTurDAn0saHHbEGF', 'wFSF3LbHQwPYy6A0pu94',"}}
[info] >      'fNgkVoyHni01zmozumbK', 'C6FceATDCJxVtji42Q3D', {"user":"    'fNgkVoyHni01zmozumbK', 'C6FceATDCJxVtji42Q3D',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'fNgkVoyHni01zmozumbK', 'C6FceATDCJxVtji42Q3D',"}}
[info] >      'M5Pahb6Vi1rdq7KZB5LF', 'oXnGAP4V78vilhIVpIQN', {"user":"    'M5Pahb6Vi1rdq7KZB5LF', 'oXnGAP4V78vilhIVpIQN',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'M5Pahb6Vi1rdq7KZB5LF', 'oXnGAP4V78vilhIVpIQN',"}}
[info] >      'NpS3CdrpHvPZClMMDeFc', 'TlS4g9PWKN1wNBtjHEoq', {"user":"    'NpS3CdrpHvPZClMMDeFc', 'TlS4g9PWKN1wNBtjHEoq',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'NpS3CdrpHvPZClMMDeFc', 'TlS4g9PWKN1wNBtjHEoq',"}}
[info] >      'rb2n32HuXJlFOk44cAkJ', 'UWkNKuXggt70Oe4bkzcx', {"user":"    'rb2n32HuXJlFOk44cAkJ', 'UWkNKuXggt70Oe4bkzcx',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'rb2n32HuXJlFOk44cAkJ', 'UWkNKuXggt70Oe4bkzcx',"}}
[info] >      'jA9Azu4oZQidKpLjPZNu', 'FwtZUF6cO4YF0pVo7CZG', {"user":"    'jA9Azu4oZQidKpLjPZNu', 'FwtZUF6cO4YF0pVo7CZG',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'jA9Azu4oZQidKpLjPZNu', 'FwtZUF6cO4YF0pVo7CZG',"}}
[info] >      '7LzTfFyZUQXm6Cl9eKL1', 'u08I4BYxrWPuwv4Byqw7', {"user":"    '7LzTfFyZUQXm6Cl9eKL1', 'u08I4BYxrWPuwv4Byqw7',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     '7LzTfFyZUQXm6Cl9eKL1', 'u08I4BYxrWPuwv4Byqw7',"}}
[info] >      'reNZlIKsdFhJiKJOhC3E' {"user":"    'reNZlIKsdFhJiKJOhC3E'","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'reNZlIKsdFhJiKJOhC3E'"}}
[info] >    ] {"user":"  ]","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   ]"}}
[info] >  } {"user":"}","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m }"}}
[info] >  🔍 REQUEST DEBUG: { {"user":"🔍 REQUEST DEBUG: {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m 🔍 REQUEST DEBUG: {"}}
[info] >    prompt: 'Normal Birth', {"user":"  prompt: 'Normal Birth',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   prompt: 'Normal Birth',"}}
[info] >    hasContext: false, {"user":"  hasContext: false,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   hasContext: false,"}}
[info] >    contextKeys: [], {"user":"  contextKeys: [],","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   contextKeys: [],"}}
[info] >    contextAction: undefined, {"user":"  contextAction: undefined,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   contextAction: undefined,"}}
[info] >    contextEditMode: undefined, {"user":"  contextEditMode: undefined,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   contextEditMode: undefined,"}}
[info] >    contextSelectedFarm: false {"user":"  contextSelectedFarm: false","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   contextSelectedFarm: false"}}
[info] >  } {"user":"}","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m }"}}
[info] >  🔍 REQUEST TYPE ANALYSIS: { {"user":"🔍 REQUEST TYPE ANALYSIS: {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m 🔍 REQUEST TYPE ANALYSIS: {"}}
[info] >    prompt: 'Normal Birth', {"user":"  prompt: 'Normal Birth',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   prompt: 'Normal Birth',"}}
[info] >    requestType: 'unknown', {"user":"  requestType: 'unknown',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   requestType: 'unknown',"}}
[info] >    hasContext: false, {"user":"  hasContext: false,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   hasContext: false,"}}
[info] >    contextAction: undefined {"user":"  contextAction: undefined","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   contextAction: undefined"}}
[info] >  } {"user":"}","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m }"}}
[info] >  📨 Context received: null {"user":"📨 Context received: null","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m 📨 Context received: null"}}
[info] >  📨 Context action: undefined {"user":"📨 Context action: undefined","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m 📨 Context action: undefined"}}
[info] >  📨 Context needsAnimalSelection: undefined {"user":"📨 Context needsAnimalSelection: undefined","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m 📨 Context needsAnimalSelection: undefined"}}
[info] >  Chat response: Normal birth refers to a vaginal delivery that occurs without the need for medical interventions such as induction, epidural anesthesia, or cesarean section. This process usually involves the natural progression of labor and delivery with minimal medical interference. It is important for pregnant individuals to discuss their birth preferences and options with their healthcare provider to determine the best plan for their individual situation. {"user":"Chat response: Normal birth refers to a vaginal delivery that occurs without the need for medical interventions such as induction, epidural anesthesia, or cesarean section. This process usually involves the natural progression of labor and delivery with minimal medical interference. It is important for pregnant individuals to discuss their birth preferences and options with their healthcare provider to determine the best plan for their individual situation.","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m Chat response: Normal birth refers to a vaginal delivery that occurs without the need for medical interventions such as induction, epidural anesthesia, or cesarean section. This process usually involves the natural progression of labor and delivery with minimal medical interference. It is important for pregnant individuals to discuss their birth preferences and options with their healthcare provider to determine the best plan for their individual situation."}}
[debug] [2025-07-30T13:31:13.045Z] Finishing up request with event=pause {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=pause"}}
[info] i  functions: Finished "australia-southeast1-animalApp" in 1602.7098999999998ms {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finished \"australia-southeast1-animalApp\" in 1602.7098999999998ms"}}
[debug] [2025-07-30T13:31:13.046Z] [worker-australia-southeast1-animalApp-ba127438-ffbd-44b0-b3d0-c441c528b36f]: IDLE {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[worker-australia-southeast1-animalApp-ba127438-ffbd-44b0-b3d0-c441c528b36f]: IDLE"}}
[debug] [2025-07-30T13:31:13.046Z] Finishing up request with event=finish {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=finish"}}
[debug] [2025-07-30T13:31:13.046Z] Finishing up request with event=close {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=close"}}
[debug] [2025-07-30T13:31:13.046Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":[],"workRunningCount":0}
