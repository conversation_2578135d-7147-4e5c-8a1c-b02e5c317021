/**
 * Health Record Flow Test Script
 * Tests the complete birth record creation flow to identify where it breaks
 */

const axios = require('axios');

// Configuration
const BASE_URL = 'http://localhost:5001/your-project/australia-southeast1/animalApp';
const TEST_USER_ID = 'test-user-123';

// Test data
const testData = {
  userId: TEST_USER_ID,
  language: 'en'
};

// Helper function to make API calls
async function makeRequest(prompt, context = null) {
  try {
    const payload = {
      ...testData,
      prompt,
      ...(context && { context })
    };
    
    console.log(`\n🔍 REQUEST: "${prompt}"`);
    console.log('📤 Payload:', JSON.stringify(payload, null, 2));
    
    const response = await axios.post(BASE_URL, payload);
    
    console.log('📥 Response Status:', response.status);
    console.log('📥 Response Data:', JSON.stringify(response.data, null, 2));
    
    return response.data;
  } catch (error) {
    console.error('❌ Request failed:', error.message);
    if (error.response) {
      console.error('❌ Response data:', error.response.data);
    }
    throw error;
  }
}

// Test functions
async function testBirthOptionSelection() {
  console.log('\n=== TEST 1: Birth Option Selection ===');
  const response = await makeRequest('Add Birth');
  
  // Verify response
  if (response.optionCards && response.optionCards.length === 5) {
    console.log('✅ Birth options displayed correctly');
    return response.context;
  } else {
    console.log('❌ Birth options not displayed correctly');
    throw new Error('Birth option selection failed');
  }
}

async function testOptionSelection(context) {
  console.log('\n=== TEST 2: Select Normal Birth ===');
  const response = await makeRequest('normal-birth', context);
  
  // Verify response
  if (response.animalImages && response.context?.needsAnimalSelection) {
    console.log('✅ Animal selection phase reached');
    console.log('🔍 Context after option selection:', {
      needsAnimalSelection: response.context.needsAnimalSelection,
      needsRecordOptionSelection: response.context.needsRecordOptionSelection,
      hasHealthRecordData: !!response.context.healthRecordData,
      recordOption: response.context.healthRecordData?.recordOption
    });
    return response.context;
  } else {
    console.log('❌ Animal selection phase not reached');
    throw new Error('Option selection failed');
  }
}

async function testAnimalSelection(context) {
  console.log('\n=== TEST 3: Select Animal ===');
  
  // Use the first available animal ID from context
  const animalId = context.availableAnimals?.[0]?.id || '6fUQYYUsll91XIaLi016';
  console.log('🐄 Selecting animal ID:', animalId);
  
  const response = await makeRequest(animalId, context);
  
  // Verify response
  if (response.context?.readyToSave && response.context?.selectedAnimal) {
    console.log('✅ Animal selected successfully');
    console.log('🔍 Context after animal selection:', {
      readyToSave: response.context.readyToSave,
      needsAnimalSelection: response.context.needsAnimalSelection,
      needsRecordOptionSelection: response.context.needsRecordOptionSelection,
      hasSelectedAnimal: !!response.context.selectedAnimal,
      recordOption: response.context.healthRecordData?.recordOption
    });
    return response.context;
  } else {
    console.log('❌ Animal selection failed');
    console.log('🔍 Actual context:', response.context);
    throw new Error('Animal selection failed');
  }
}

async function testYesResponse(context) {
  console.log('\n=== TEST 4: Confirm Save ===');
  const response = await makeRequest('Yes', context);
  
  // Verify response
  if (response.healthRecordSaved) {
    console.log('✅ Health record saved successfully');
    return true;
  } else {
    console.log('❌ Health record save failed');
    console.log('🔍 Response:', response);
    throw new Error('Yes response failed');
  }
}

// Main test function
async function runCompleteTest() {
  try {
    console.log('🚀 Starting Health Record Flow Test');
    console.log('📍 Testing URL:', BASE_URL);
    
    // Test 1: Birth option selection
    const context1 = await testBirthOptionSelection();
    
    // Test 2: Select normal birth option
    const context2 = await testOptionSelection(context1);
    
    // Test 3: Select animal
    const context3 = await testAnimalSelection(context2);
    
    // Test 4: Confirm save
    await testYesResponse(context3);
    
    console.log('\n🎉 ALL TESTS PASSED! Health record flow is working correctly.');
    
  } catch (error) {
    console.log('\n💥 TEST FAILED:', error.message);
    console.log('🔍 This indicates where the flow is breaking.');
    process.exit(1);
  }
}

// Run the test
if (require.main === module) {
  runCompleteTest();
}

module.exports = {
  runCompleteTest,
  testBirthOptionSelection,
  testOptionSelection,
  testAnimalSelection,
  testYesResponse
};
